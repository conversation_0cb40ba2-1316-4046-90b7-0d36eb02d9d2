DECLARE 
    result json;
    name_parts text[];
    total_count_result bigint;
BEGIN
    -- Split the search text into parts for names
    name_parts := string_to_array(search_text, ' ');

    -- Get total count separately (more efficient)
    SELECT COUNT(*) INTO total_count_result
    FROM clients c
    WHERE 
        -- Province filter
        (province_filter IS NULL OR 
            CASE 
                WHEN c.province = 'Other' AND (c.state->>'name') IS NOT NULL THEN 
                    CASE 
                        WHEN (c.state->>'name') = ANY(province_filter) THEN TRUE
                        WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                        WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                        WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                        ELSE FALSE
                    END
                WHEN c.province = (c.state->>'name') THEN c.province::text = ANY(province_filter)
                WHEN (c.state->>'name') IS NOT NULL THEN 
                    CASE 
                        WHEN (c.state->>'name') = ANY(province_filter) THEN TRUE
                        WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                        WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                        WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                        ELSE FALSE
                    END
                ELSE 
                    CASE 
                        WHEN c.province::text = ANY(province_filter) THEN TRUE
                        WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                        WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                        WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                        ELSE FALSE
                    END
            END
        )
        -- Other filters
        AND (lead_quality_filter IS NULL OR c.lead_quality::text = ANY(lead_quality_filter))
        AND (stage_filter IS NULL OR c.stage::text = ANY(stage_filter))
        AND (slp_filter IS NULL OR c.active_slp = ANY(slp_filter))
        AND (organization_filter IS NULL OR c.organization_id::bigint = organization_filter)
        AND (goals_filter IS NULL OR c.goals && goals_filter)
        AND (group_filter IS NULL OR EXISTS (
            SELECT 1 FROM tags tg 
            WHERE tg.client_id = c.id 
            AND tg.category = 'group'
            AND tg.tag_name = ANY(group_filter)
        ))
        AND (active_client IS NULL OR 
             (active_client = true AND c.slp_notes != 'Do Not Contact') OR 
             (active_client = false AND c.slp_notes = 'Do Not Contact'))
        -- Consulted by filter
        AND (consulted_by IS NULL OR c.consulted_by = ANY(consulted_by))
        -- Referral source filter  
        AND (referral_source IS NULL OR c.referral_source = ANY(referral_source))
        -- Search text
        AND (
            search_text = '' OR 
            (array_length(name_parts, 1) > 0 AND (
                (LOWER(c.first_name) LIKE LOWER(name_parts[1] || '%') AND LOWER(c.last_name) LIKE LOWER(name_parts[2] || '%')) 
                OR 
                (LOWER(c.first_name) LIKE LOWER(name_parts[2] || '%') AND LOWER(c.last_name) LIKE LOWER(name_parts[1] || '%'))
                OR 
                (array_length(name_parts, 1) = 1 AND 
                 (LOWER(c.first_name) LIKE LOWER(name_parts[1] || '%') OR LOWER(c.last_name) LIKE LOWER(name_parts[1] || '%')))
            ))
            OR LOWER(c.first_name) LIKE LOWER('%' || search_text || '%')
            OR LOWER(c.display_name) LIKE LOWER('%' || search_text || '%')
            OR LOWER(c.middle_name) LIKE LOWER('%' || search_text || '%')
            OR LOWER(c.last_name) LIKE LOWER('%' || search_text || '%')
           -- OR LOWER(c.email) LIKE LOWER('%' || search_text || '%')
            OR (search_text ~ '[0-9]' AND regexp_replace(c.phone, '[^0-9]', '', 'g') LIKE '%' || regexp_replace(search_text, '[^0-9]', '', 'g') || '%')
            OR EXISTS (
                SELECT 1 FROM client_emails ce 
                WHERE ce.client_id = c.id 
                AND LOWER(ce.email) LIKE LOWER('%' || search_text || '%')
            )
        );

    -- Get paginated results with aggregated data
    SELECT json_build_object(
        'data', (
            SELECT json_agg(
                to_jsonb(c) || 
                jsonb_build_object(
                    'emails', COALESCE((
                        SELECT json_agg(
                            json_build_object(
                                'email', ce.email,
                                'is_primary_email', ce.is_primary_email
                            )
                        )
                        FROM client_emails ce
                        WHERE ce.client_id = c.id
                    ), '[]'::json),
                    'groups', COALESCE((
                        SELECT array_agg(DISTINCT tg.tag_name)
                        FROM tags tg
                        WHERE tg.client_id = c.id AND tg.category = 'group'
                    ), ARRAY[]::text[]),
                    'tags', COALESCE((
                        SELECT array_agg(DISTINCT tg.tag_name)
                        FROM tags tg
                        WHERE tg.client_id = c.id AND tg.category != 'group'
                    ), ARRAY[]::text[]),
                    'invoices', (
                        SELECT COALESCE(jsonb_agg(to_jsonb(i)), '[]'::jsonb)
                        FROM invoices i
                        WHERE i.client_id = c.id
                    ),
                    'packages', (
                        SELECT COALESCE(jsonb_agg(to_jsonb(p)), '[]'::jsonb)
                        FROM packages p
                        WHERE p.client_id = c.id
                    )
                )
            )
            FROM (
                SELECT c.*
                FROM clients c
                WHERE 
                    -- Same filters as count query
                    (province_filter IS NULL OR 
                        CASE 
                            WHEN c.province = 'Other' AND (c.state->>'name') IS NOT NULL THEN 
                                CASE 
                                    WHEN (c.state->>'name') = ANY(province_filter) THEN TRUE
                                    WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                                    WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                                    WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                                    ELSE FALSE
                                END
                            WHEN c.province = (c.state->>'name') THEN c.province::text = ANY(province_filter)
                            WHEN (c.state->>'name') IS NOT NULL THEN 
                                CASE 
                                    WHEN (c.state->>'name') = ANY(province_filter) THEN TRUE
                                    WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                                    WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                                    WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                                    ELSE FALSE
                                END
                            ELSE 
                                CASE 
                                    WHEN c.province::text = ANY(province_filter) THEN TRUE
                                    WHEN 'Other - Canada' = ANY(province_filter) AND (c.country->>'name') = 'Canada' THEN TRUE
                                    WHEN 'Other - United States' = ANY(province_filter) AND (c.country->>'name') = 'United States' THEN TRUE
                                    WHEN 'Other - International' = ANY(province_filter) AND (c.country->>'name') NOT IN ('Canada', 'United States') THEN TRUE
                                    ELSE FALSE
                                END
                        END
                    )
                    AND (lead_quality_filter IS NULL OR c.lead_quality::text = ANY(lead_quality_filter))
                    AND (stage_filter IS NULL OR c.stage::text = ANY(stage_filter))
                    AND (slp_filter IS NULL OR c.active_slp = ANY(slp_filter))
                    AND (organization_filter IS NULL OR c.organization_id::bigint = organization_filter)
                    AND (goals_filter IS NULL OR c.goals && goals_filter)
                    AND (group_filter IS NULL OR EXISTS (
                        SELECT 1 FROM tags tg 
                        WHERE tg.client_id = c.id 
                        AND tg.category = 'group'
                        AND tg.tag_name = ANY(group_filter)
                    ))
                    AND (active_client IS NULL OR 
                         (active_client = true AND c.slp_notes != 'Do Not Contact') OR 
                         (active_client = false AND c.slp_notes = 'Do Not Contact'))
                    -- Consulted by filter
                    AND (consulted_by IS NULL OR c.consulted_by = ANY(consulted_by))
                    -- Referral source filter  
                    AND (referral_source IS NULL OR c.referral_source = ANY(referral_source))
                    AND (
                        search_text = '' OR 
                        (array_length(name_parts, 1) > 0 AND (
                            (LOWER(c.first_name) LIKE LOWER(name_parts[1] || '%') AND LOWER(c.last_name) LIKE LOWER(name_parts[2] || '%')) 
                            OR 
                            (LOWER(c.first_name) LIKE LOWER(name_parts[2] || '%') AND LOWER(c.last_name) LIKE LOWER(name_parts[1] || '%'))
                            OR 
                            (array_length(name_parts, 1) = 1 AND 
                             (LOWER(c.first_name) LIKE LOWER(name_parts[1] || '%') OR LOWER(c.last_name) LIKE LOWER(name_parts[1] || '%')))
                        ))
                        OR LOWER(c.first_name) LIKE LOWER('%' || search_text || '%')
                        OR LOWER(c.display_name) LIKE LOWER('%' || search_text || '%')
                        OR LOWER(c.middle_name) LIKE LOWER('%' || search_text || '%')
                        OR LOWER(c.last_name) LIKE LOWER('%' || search_text || '%')
                        --OR LOWER(c.email) LIKE LOWER('%' || search_text || '%')
                        OR (search_text ~ '[0-9]' AND regexp_replace(c.phone, '[^0-9]', '', 'g') LIKE '%' || regexp_replace(search_text, '[^0-9]', '', 'g') || '%')
                        OR EXISTS (
                            SELECT 1 FROM client_emails ce 
                            WHERE ce.client_id = c.id 
                            AND LOWER(ce.email) LIKE LOWER('%' || search_text || '%')
                        )
                    )
                ORDER BY c.id DESC 
                LIMIT page_limit OFFSET page_offset
            ) c
        ),
        'total_count', total_count_result
    )
    INTO result;

    RETURN result;
END;
