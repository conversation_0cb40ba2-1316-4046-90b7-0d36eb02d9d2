// lib/stripe.ts
import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';
import <PERSON><PERSON> from 'stripe';
// import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia', // Latest as of 2025
});

// const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
// const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!; // Use service role for server-side
// const supabase = createClient(supabaseUrl, supabaseKey);

// Type for therapist (adjust based on your schema)
// interface Therapist {
//   id: string;
//   stripe_account_id?: string;
// }

// /**
//  * Get the current therapist from Supabase (assumes user is authenticated).
//  * @param userId Supabase user ID
//  */
// async function getTherapist(userId: string): Promise<Therapist | null> {
//   const { data, error } = await supabase
//     .from('therapists')
//     .select('*')
//     .eq('user_id', userId)
//     .single();

//   if (error) throw new Error(`Failed to fetch therapist: ${error.message}`);
//   return data;
// }

/**
 * Save Stripe account ID to org in Supabase.
 * @param user_id Therapist ID
 * @param stripeAccountId Connected account ID
 */
async function saveStripeAccountId(
  user_id: string,
  stripeAccountId: string,
  supabase: SupabaseClient
): Promise<void> {
  const { error } = await supabase
    .from(tableNames.organizations)
    .update({ stripe_user_id: stripeAccountId })
    .eq('owner', Number(user_id));

  if (error) throw new Error(`Failed to save Stripe ID: ${error.message}`);
}

/**
 * Generate Stripe Connect OAuth URL.
 * Robust: Includes state for CSRF protection.
 * @param state Random state (e.g., session ID) for security
 */
export function generateStripeConnectUrl(state: string): string {
  const params = new URLSearchParams({
    client_id: process.env.STRIPE_CONNECT_CLIENT_ID!,
    scope: 'read_write', // Full access for payments
    response_type: 'code',
    redirect_uri: `${process.env.NEXT_PUBLIC_SITE_URL}/api/stripe/connect-callback`,
    state,
    // 'stripe_user[email]': '', // Pre-fill if you have user email
  });

  return `https://connect.stripe.com/oauth/authorize?${params.toString()}`;
}

/**
 * Handle OAuth callback: Exchange code for tokens and account ID.
 * @param code Authorization code from Stripe
 * @param state Received state to validate
 * @param expectedState Expected state for CSRF check
 * @param therapistId Therapist ID to associate
 * @returns Connected account ID
 */
export async function handleStripeConnectCallback(
  code: string,
  state: string,
  expectedState: string,
  user_id: string,
  supabase: SupabaseClient
): Promise<string> {
  if (state !== expectedState)
    throw new Error('Invalid state: Possible CSRF attack');

  try {
    const response = await fetch('https://connect.stripe.com/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_secret: process.env.STRIPE_SECRET_KEY!,
        code,
        grant_type: 'authorization_code',
      }),
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error_description);

    const stripeAccountId = data.stripe_user_id;
    await saveStripeAccountId(user_id, stripeAccountId, supabase);
    return stripeAccountId;
  } catch (error) {
    throw new Error(`OAuth exchange failed: ${(error as Error).message}`);
  }
}

/**
 * Create a product on the connected account.
 * Reusable for services (e.g., therapy session).
 * @param stripeAccountId Connected account ID
 * @param name Product name (e.g., "1-Hour Therapy Session")
 * @param description Optional description
 */
export async function createProduct(
  stripeAccountId: string,
  name: string,
  description?: string
): Promise<Stripe.Product> {
  try {
    return await stripe.products.create(
      { name, description },
      { stripeAccount: stripeAccountId }
    );
  } catch (error) {
    throw new Error(`Product creation failed: ${(error as Error).message}`);
  }
}

/**
 * Create a price for a product on the connected account.
 * @param stripeAccountId Connected account ID
 * @param productId Product ID from createProduct
 * @param amount Price in cents (e.g., 5000 for $50)
 * @param currency e.g., 'usd'
 * @param recurring Optional: { interval: 'month' } for subscriptions
 */
export async function createPrice(
  stripeAccountId: string,
  productId: string,
  amount: number,
  currency: string = 'usd',
  recurring?: Stripe.PriceCreateParams.Recurring
): Promise<Stripe.Price> {
  try {
    return await stripe.prices.create(
      {
        product: productId,
        unit_amount: amount,
        currency,
        recurring,
      },
      { stripeAccount: stripeAccountId }
    );
  } catch (error) {
    throw new Error(`Price creation failed: ${(error as Error).message}`);
  }
}

/**
 * Generate a payment link for a service.
 * Robust: Supports quantity, metadata for service details.
 * @param stripeAccountId Connected account ID
 * @param priceId Price ID from createPrice
 * @param quantity Default 1
 * @param serviceDetails Metadata (e.g., { session_type: 'Individual', duration: '60min' })
 * @param successUrl URL after payment
 * @param cancelUrl URL if canceled
 */
export async function generatePaymentLink(
  stripeAccountId: string,
  priceId: string,
  quantity: number = 1,
  serviceDetails: Record<string, string> = {},
  successUrl: string = 'https://yourapp.com/success'
  //   cancelUrl: string = 'https://yourapp.com/cancel'
): Promise<Stripe.PaymentLink> {
  try {
    return await stripe.paymentLinks.create(
      {
        line_items: [{ price: priceId, quantity }],
        after_completion: { type: 'redirect', redirect: { url: successUrl } },
        metadata: serviceDetails,
        // Optional: application_fee_amount: 500, // e.g., 5% fee in cents (requires Transfer setup)
      },
      { stripeAccount: stripeAccountId }
    );
  } catch (error) {
    throw new Error(`Payment link failed: ${(error as Error).message}`);
  }
}

export { stripe }; // Export for other uses
