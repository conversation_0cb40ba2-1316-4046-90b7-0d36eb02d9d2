export const KLAVIYO_CONFIG = {
  clientId: process.env.KLAVIYO_CLIENT_ID!,
  clientSecret: process.env.KLAVIYO_CLIENT_SECRET!,
  redirectUri: `${process.env.NEXT_PUBLIC_SITE_URL}/api/auth/klaviyo/callback`,
  publicApiKey: process.env.NEXT_PUBLIC_KLAVIYO_PUBLIC_API_KEY!,
  apiSecretKey: process.env.KLAVIYO_API_SECRET_KEY!,
  baseUrl: 'https://a.klaviyo.com',
  apiUrl: 'https://a.klaviyo.com/api',
  authUrl: 'https://www.klaviyo.com/oauth/authorize',
  tokenUrl: 'https://a.klaviyo.com/oauth/token',
  // Required scopes - accounts:read is mandatory
  scopes: [
    'accounts:read',
    'profiles:read',
    'profiles:write',
    'events:read',
    'events:write',
    'lists:read',
    'lists:write',
    'segments:read',
    'campaigns:read',
    'campaigns:write',
    'flows:read',
    'flows:write',
  ],
};
