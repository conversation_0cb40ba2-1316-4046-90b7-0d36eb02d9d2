import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';
import { KlaviyoAPI, KlaviyoEvent } from './api';
import { KlaviyoActions } from '@/constants/klaviyo-actions';

export const getKlaviyoOrganization = async (
  organization_id: string,
  supabase: SupabaseClient
) => {
  const { data: KlaviyoDetails, error: DetailsError } = await supabase
    .from(tableNames.klaviyo_organization)
    .select('*')
    .eq('organization_id', organization_id)
    .maybeSingle();
  if (DetailsError) throw DetailsError;
  return KlaviyoDetails;
};
export const updateKlaviyoOrganization = async (data: {
  organization_id: string;
  payload: any;
  supabase: SupabaseClient;
}) => {
  const { error: DetailsError } = await data.supabase
    .from(tableNames.klaviyo_organization)
    .update(data.payload)
    .eq('organization_id', data.organization_id);
  if (DetailsError) throw DetailsError;
  return 'success';
};
export const dispatchBookingCreatedEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: any
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );

    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    await klaviyoApi.trackEvent(
      organization_id,
      KlaviyoActions.BOOKING_CREATED,
      event,
      supabase
    );
  } catch (klaviyoError: any) {
    console.warn('Failed to track Klaviyo event in service"', klaviyoError);
  }
};
export const dispatchKlaviyoEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: KlaviyoEvent,
  eventType: string
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );
    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    if (event?.data?.attributes?.profile?.data?.attributes?.id) {
      const { data: ClientDetails } = await supabase
        .from(tableNames.clients)
        .select('*')
        .eq('id', event?.data?.attributes?.profile?.data?.attributes?.id)
        .maybeSingle();

      // console.log(
      //   'profile is ',
      //   event?.data?.attributes?.profile?.data?.attributes
      // );
      // console.log('ClientDetails is ', ClientDetails);
      const updatedProps = {
        ...event?.data?.attributes?.profile?.data?.attributes?.properties,
        ...(ClientDetails?.province && { province: ClientDetails.province }),
        ...(ClientDetails?.country?.name && {
          country: ClientDetails.country.name,
        }),
        ...(ClientDetails?.state?.name && { state: ClientDetails.state.name }),
        ...(ClientDetails?.city?.name && { city: ClientDetails.city.name }),
        ...(ClientDetails?.address && { address: ClientDetails.address }),
        ...(ClientDetails?.phone && { phone: ClientDetails.phone }),
        ...(ClientDetails?.utm_source && {
          utm_source: ClientDetails.utm_source,
        }),
        ...(ClientDetails?.utm_medium && {
          utm_medium: ClientDetails.utm_medium,
        }),
        ...(ClientDetails?.utm_campaign && {
          utm_campaign: ClientDetails.utm_campaign,
        }),
        ...(ClientDetails?.utm_content && {
          utm_content: ClientDetails.utm_content,
        }),
        ...(ClientDetails?.stage && {
          stage: ClientDetails.stage,
        }),
        ...(ClientDetails?.email_marketing_consent && {
          consent: ['EMAIL'],
        }),
      };

      event.data.attributes.profile.data.attributes.properties = updatedProps;
    }
    delete event.data.attributes.profile.data.attributes.id;
    // console.log('organization_id is ', organization_id);
    // console.log('eventType is ', eventType);
    // console.log('event is ', event?.data?.attributes?.profile);

    await klaviyoApi.trackEvent(organization_id, eventType, event, supabase);
    const { data: KlaviyoOrg } = await supabase
      .from(tableNames.klaviyo_organization)
      .select('*')
      .eq('organization_id', organization_id)
      .maybeSingle();
    const profile = await klaviyoApi.getOrCreateProfile(
      event?.data?.attributes?.profile?.data?.attributes?.email
    );
    console.log('profile is ', profile);

    await klaviyoApi.addProfileToList(KlaviyoOrg?.list_id, profile?.id);
  } catch (klaviyoError: any) {
    console.warn('', klaviyoError);
  }
};
