import { atom } from 'recoil';

export interface IAllClientsFilterState {
  provinceFilter: Array<string>;
  slpFilter: Array<number>;
  stageFilter: Array<string>;
  leadFilter: Array<string>;
  goal: Array<string>;
  group: Array<string>;
  search: string;
  org_id?: any;
  page: number;
  size: number;
  active_clients: Array<string>;
  currentPage: number;
  total: number;
  consulted_by: Array<string>;
  referral_source: Array<string>;
}
export const AllClientsFilterState = atom<IAllClientsFilterState>({
  key: 'AllClientsFilterState',
  default: {
    provinceFilter: [],
    active_clients: [],
    search: '',
    slpFilter: [],
    org_id: null,
    goal: [],
    group: [],
    stageFilter: [],
    leadFilter: [],
    page: 1,
    size: 50,
    currentPage: 1,
    total: 0,
    consulted_by: [],
    referral_source: [],
  },
});
export interface IAllClientsMatchedFilterState {
  provinceFilter: Array<string>;
  slpFilter: Array<number>;
  stageFilter: Array<string>;
  leadFilter: Array<string>;
  goal: Array<string>;
  search: string;
}
export const AllClientsMatchedFilterState = atom<IAllClientsMatchedFilterState>(
  {
    key: 'AllClientsMatchedFilterState',
    default: {
      provinceFilter: [],
      search: '',
      slpFilter: [],
      goal: [],
      stageFilter: [],
      leadFilter: [],
    },
  }
);
