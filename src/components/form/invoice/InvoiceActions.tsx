// components/invoice/InvoiceActions.jsx
import React from 'react';
import { Box } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import { Colors } from '@/constants/colors';

export default function InvoiceActions({
  isLoading,
  onSubmit,
  isEditing,
}: any) {
  return (
    <Box p={6} pt={0} w={'full'} display={'flex'} justifyContent={'flex-end'}>
      <Button
        loading={isLoading}
        onClick={onSubmit}
        bg={Colors?.ORANGE?.PRIMARY}
        color="white"
        px={8}
        py={6}
        fontSize="lg"
      >
        {isEditing ? 'Save Invoice' : 'Create Invoice'}
      </Button>
    </Box>
  );
}
