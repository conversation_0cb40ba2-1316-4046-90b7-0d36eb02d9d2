// components/invoice/InvoiceTotals.jsx
import { Colors } from '@/constants/colors';
import { Box, Separator, Text } from '@chakra-ui/react';
import { FiPlusCircle } from 'react-icons/fi';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import { currencyOptions } from '@/data/options';

export default function InvoiceTotals({
  invoiceDetails,
  discountData,
  discountDisclosure,
  handleInvoiceDetailsChange,
  subTotalWithDiscount,
  taxAmounts,
  totalTaxAmount,
}: any) {
  // const formatCurrency = (amount: number, currencyCode: string) => {
  //   return new Intl.NumberFormat('en-US', {
  //     style: 'currency',
  //     currency: currencyCode || 'USD',
  //   }).format(amount);
  // };
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const currencyCode = org?.currency_code || 'CAD';

  return (
    <Box px={{ md: 6 }}>
      <Box
        display={'flex'}
        flexDirection={'column'}
        w={'full'}
        justifyContent={'flex-end'}
        alignItems={'end'}
        maxW={'600px'}
        ml={'auto'}
      >
        {/* Subtotal */}
        <Box
          display={'grid'}
          gridTemplateColumns={'1fr auto'}
          alignItems={'center'}
          gap={8}
          pb={4}
          w={'full'}
          minW={'400px'}
        >
          <Text
            textAlign={'right'}
            fontSize={{ base: 'md', md: 'base' }}
            color="gray.600"
          >
            Subtotal
          </Text>
          <Text
            textAlign={'right'}
            fontSize={{ base: 'md', md: 'base' }}
            minW={'120px'}
          >
            {formatMoney(Number(invoiceDetails?.invoiceSubTotal || 0), {
              currencyCode: invoiceDetails?.currency_code,
            })}
          </Text>
        </Box>

        {/* Discount if discount exist */}
        <Box
          display={'grid'}
          gridTemplateColumns={'1fr auto'}
          alignItems={'center'}
          gap={8}
          pb={4}
          w={'full'}
          minW={'400px'}
        >
          <Box
            display={'flex'}
            justifyContent={'flex-end'}
            gap={1}
            alignItems={'center'}
            color={Colors?.ORANGE?.PRIMARY}
            cursor={'pointer'}
            onClick={discountDisclosure.onOpen}
          >
            <FiPlusCircle size="12px" />
            <Text fontWeight={'semibold'} fontSize={{ base: 'md', md: 'base' }}>
              {discountData.value > 0 ? 'Edit discount' : 'Add a discount'}
            </Text>
          </Box>
          <Text textAlign={'right'} minW={'120px'}>
            {discountData.value > 0
              ? discountData.type === 'percentage'
                ? `-${discountData.value}% (${formatMoney(
                    Number(invoiceDetails?.discount || 0),
                    { currencyCode: invoiceDetails?.currency_code }
                  )})`
                : `-${formatMoney(Number(invoiceDetails?.discount || 0), {
                    currencyCode: invoiceDetails?.currency_code,
                  })}`
              : ''}
          </Text>
        </Box>

        {/* Subtotal with Discount if discount exist */}
        {invoiceDetails?.discount ? (
          <Box
            display={'grid'}
            gridTemplateColumns={'1fr auto'}
            alignItems={'center'}
            gap={8}
            pb={4}
            w={'full'}
            minW={'400px'}
          >
            <Text
              fontSize={{ base: 'md', md: 'base' }}
              textAlign={'right'}
              color="gray.600"
              wordBreak={'break-word'}
            >
              Subtotal after discount
            </Text>
            <Text
              fontSize={{ base: 'md', md: 'base' }}
              textAlign={'right'}
              minW={'120px'}
            >
              {formatMoney(subTotalWithDiscount, {
                currencyCode: invoiceDetails?.currency_code,
              })}
            </Text>
          </Box>
        ) : null}

        {/* Individual Taxes */}
        {taxAmounts.length > 0 && (
          <>
            {taxAmounts.map((tax: any) => (
              <Box
                key={tax.id}
                display={'grid'}
                gridTemplateColumns={'1fr auto'}
                alignItems={'center'}
                gap={8}
                pb={4}
                w={'full'}
                minW={'400px'}
              >
                <Text
                  fontSize={{ base: 'md', md: 'base' }}
                  textAlign={'right'}
                  color="gray.600"
                >
                  {tax.name} ({tax.rate}%)
                </Text>
                <Text
                  textAlign={'right'}
                  minW={'120px'}
                  fontWeight="medium"
                  fontSize="sm"
                >
                  {/* tax.amount * subtotal_after_discount  */}
                  {formatMoney(tax.amount, {
                    currencyCode: invoiceDetails?.currency_code,
                  })}
                </Text>
              </Box>
            ))}
          </>
        )}

        {/* Total */}
        <Box
          display={'grid'}
          gridTemplateColumns={{ base: '1fr auto', md: '1fr auto auto' }}
          alignItems={'center'}
          gap={{ base: 8, md: 4 }}
          pb={4}
          w={'full'}
          minW={'400px'}
        >
          <Text textAlign={'right'} fontWeight="bold" fontSize="md">
            Total
          </Text>
          {/* Currency code */}
          <Box
            minW={'120px'}
            display={{ base: 'none', md: 'flex' }}
            justifyContent={'flex-end'}
          >
            <CustomSelect
              placeholder="Select Currency"
              onChange={(val) =>
                handleInvoiceDetailsChange('currency_code', val.value)
              }
              options={currencyOptions}
              defaultValue={currencyOptions?.find(
                (option) => option.value === currencyCode
              )}
            />
          </Box>
          <Text
            textAlign={'right'}
            minW={'120px'}
            fontWeight="bold"
            fontSize="md"
          >
            {/* subTotalAFterDiscount + AllTaxes */}
            {formatMoney(subTotalWithDiscount + totalTaxAmount, {
              currencyCode: invoiceDetails?.currency_code,
            })}
          </Text>
        </Box>
        <Box
          minW={'120px'}
          display={{ base: 'block', md: 'none' }}
          justifyContent={'flex-end'}
        >
          <CustomSelect
            placeholder="Select Currency"
            onChange={(val) =>
              handleInvoiceDetailsChange('currency_code', val.value)
            }
            options={currencyOptions}
            defaultValue={currencyOptions?.find(
              (option) => option.value === currencyCode
            )}
          />
        </Box>

        {/* Amount Due */}
        <Box
          borderTop={'1px solid #E2E8F0'}
          display={'grid'}
          gridTemplateColumns={'1fr auto'}
          alignItems={'center'}
          gap={8}
          pb={4}
          w={'full'}
          minW={'400px'}
          pt={4}
        >
          <Text textAlign={'right'} fontWeight="bold" fontSize="md">
            Amount Due
          </Text>
          <Text
            textAlign={'right'}
            minW={'120px'}
            fontWeight="bold"
            fontSize="md"
            color={Colors?.ORANGE?.PRIMARY}
          >
            {formatMoney(Number(invoiceDetails?.amountDue), {
              currencyCode: invoiceDetails?.currency_code,
            })}
          </Text>
        </Box>
      </Box>

      <Separator my={2} borderColor="#636D79" />
      <Box my={{ base: '1.4rem', md: '4rem' }} w={{ base: '100%', md: '50%' }}>
        <Text
          whiteSpace={'nowrap'}
          color={'gray.600'}
          fontWeight="medium"
          minW="8rem"
        >
          Notes / Terms
        </Text>
        <CustomTextArea
          inputProps={{
            width: '100%',
            placeholder: 'Start typing here...',
            value: invoiceDetails?.memo,
            onChange: (e: any) =>
              handleInvoiceDetailsChange('memo', e.target.value),
            borderRadius: '0',
            minH: '80px',
            resize: 'vertical',
          }}
        />
      </Box>
    </Box>
  );
}
