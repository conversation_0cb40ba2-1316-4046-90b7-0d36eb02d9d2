// components/invoice/InvoiceForm.jsx
'use client';
import { Box, Center, Text, Flex, Separator } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { FiPlus, FiPlusCircle } from 'react-icons/fi';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/button';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import InvoiceHeader from './InvoiceHeader';
import InvoiceItemTable from './InvoiceItemsTable';
import InvoiceTotals from './InvoiceTotals';
import InvoiceActions from './InvoiceActions';
// import { DiscountModal } from '@/app/(dashboard)/create-invoice/_components/DiscountModal';
import TaxForm from '@/app/(dashboard)/create-invoice/_components/TaxForm';
import GoBack from '@/components/elements/GoBack';
import { DiscountModal } from './DiscountModal';
// import StringInput from '@/components/Input/StringInput';
import CustomSelect from '@/components/Input/CustomSelect';
import { generateUUID } from '@/api/package-offerings/get-package-offering-by-slp';
import { useInvoiceForm } from './useInvoiceForm';
import AnimateLoader from '@/components/elements/loader/animate-loader';

// Import sub-components
const placeHolderItem = {
  id: null,
  organization_id: null,
  created_by: null,
  name: '',
  description: '',
  price: 0,
  duration_minutes: 45,
  status: 'ACTIVE',
  currency_code: 'USD',
  taxes: [],
  new_item: true,
  type: 'service',
};

export default function InvoiceForm({ initialData = {} }) {
  const invoiceFormHook = useInvoiceForm(initialData);
  const {
    itemDisclosure,
    deleteItem,
    updateItem,
    Client,
    handleCreateInvoice,
    createInvoiceLoading,
    selectedItems,
    calculateItemTax,
    discountData,
    handleApplyDiscount,
    discountDisclosure,
    actions,
    taxOptions,
    targetItem,
    taxDisclosure,
    invoiceItemOptions,
    invoiceDetails,
    handleInvoiceDetailsChange,
    handleSelectClient,
    handleEdit,
    handleItemClick,
    UpdateInvoicePending,
    // New calculated values
    subTotalWithDiscount,
    taxAmounts,
    totalTaxAmount,
    itemLoading,
  } = invoiceFormHook;

  const router = useRouter();
  const isEditing = Object.keys(initialData).length > 0;

  return (
    <Center w={'100%'} py={{ md: 8 }}>
      {itemLoading ? (
        <AnimateLoader />
      ) : (
        <Box w={{ base: '100%', md: '85%' }} maxW="1400px">
          <GoBack onClick={() => router.back()} />

          <Box bg="white" p={{ base: 3, md: 8 }} borderRadius="lg" shadow="sm">
            <Text
              fontSize={{ base: 'xl', md: '2xl' }}
              fontWeight={'bold'}
              mb={6}
            >
              {isEditing ? 'Edit Invoice' : 'New Invoice'}
            </Text>
            {/* Invoice Header Section */}
            <InvoiceHeader
              client={Client}
              invoiceDetails={invoiceDetails}
              handleInvoiceDetailsChange={handleInvoiceDetailsChange}
              handleSelectClient={handleSelectClient}
              isEditing={isEditing}
            />

            <Separator my={8} borderColor="gray.200" />

            {/* Invoice Items Table */}
            <InvoiceItemTable
              selectedItems={selectedItems}
              itemDisclosure={itemDisclosure}
              invoiceItemOptions={invoiceItemOptions}
              handleItemClick={invoiceFormHook.handleItemClick} // Directly use from hook
              updateItem={updateItem}
              deleteItem={deleteItem}
              calculateItemTax={calculateItemTax}
              taxOptions={taxOptions}
              actions={actions}
              invoiceDetails={invoiceDetails}
            />

            {/* Add Item Button */}
            <Flex
              borderBottom="1px solid #EDF1F3"
              borderX="1px solid #EDF1F3"
              p={4}
              justifyContent="flex-start"
              mt={4}
              borderBottomRadius="md"
              alignItems={{ base: 'start', md: 'center' }}
              gap={{ base: '0.5rem', md: '1rem' }}
              flexDirection={{ base: 'column', md: 'row' }}
            >
              <Button
                onClick={itemDisclosure?.onToggle}
                color={Colors?.ORANGE?.PRIMARY}
                bg={'transparent'}
                _hover={{ bg: Colors?.ORANGE?.LIGHT }}
              >
                <FiPlus /> Add an item
              </Button>
              {itemDisclosure?.open && (
                <Box w={'full'}>
                  <CustomSelect
                    onChange={(val) => {
                      if (val?.action === 'addNewItem') {
                        handleItemClick({
                          ...placeHolderItem,
                          itemId: generateUUID(),
                        });
                      } else {
                        handleItemClick(val?.value);
                      }
                      itemDisclosure?.onClose();
                    }}
                    options={invoiceItemOptions.concat([
                      {
                        label: (
                          <Flex
                            justifyContent={'center'}
                            alignItems="center"
                            gap="0.5rem"
                          >
                            <FiPlusCircle color={Colors?.ORANGE?.PRIMARY} />
                            <Text
                              color={Colors?.ORANGE?.PRIMARY}
                              fontWeight="bold"
                              fontSize={'.75rem'}
                            >
                              Create a new item
                            </Text>
                          </Flex>
                        ),
                        value: 'add-new-item', // Dummy value for action
                        action: 'addNewItem',
                      },
                    ])}
                    selectedOption={null}
                    isClearable={true}
                    placeholder="Search for items or services..."
                  />
                </Box>
              )}
            </Flex>

            <Separator my={8} borderColor="gray.200" />

            {/* Invoice Totals Section */}
            <InvoiceTotals
              invoiceDetails={invoiceDetails}
              discountData={discountData}
              discountDisclosure={discountDisclosure}
              handleInvoiceDetailsChange={handleInvoiceDetailsChange}
              subTotalWithDiscount={subTotalWithDiscount}
              taxAmounts={taxAmounts}
              totalTaxAmount={totalTaxAmount}
            />

            {/* Final Actions */}
            <InvoiceActions
              isLoading={createInvoiceLoading || UpdateInvoicePending}
              onSubmit={isEditing ? handleEdit : handleCreateInvoice}
              isEditing={isEditing}
            />
          </Box>

          {/* Modals */}
          <DiscountModal
            isOpen={discountDisclosure.open}
            onClose={discountDisclosure.onClose}
            onApplyDiscount={handleApplyDiscount}
            currentDiscount={discountData}
          />

          <CustomModal
            open={taxDisclosure?.open}
            onOpenChange={taxDisclosure?.onClose}
            headertext="Create a new tax"
            size="md"
          >
            <TaxForm
              onClose={taxDisclosure?.onClose}
              onSuccess={(tax: any) => {
                updateItem(
                  'taxes',
                  [...(targetItem.taxes || []), tax],
                  targetItem?.itemId
                );
              }}
            />
          </CustomModal>
        </Box>
      )}
    </Center>
  );
}
