'use client';

import Script from 'next/script';

type GoogleMapsScriptProps = {
  onLoad?: () => void;
};

export default function GoogleMapsScript({ onLoad }: GoogleMapsScriptProps) {
  return (
    <Script
      src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GMAPS}&libraries=places&loading=async`}
      strategy="afterInteractive"
      onLoad={() => {
        //console.log('Google Maps API loaded');
        onLoad?.();
      }}
      onError={(e) => {
        console.error('Failed to load Google Maps API:', e);
      }}
    />
  );
}
