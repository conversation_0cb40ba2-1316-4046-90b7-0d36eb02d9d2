import React from 'react';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';
import { Badge, HStack } from '@chakra-ui/react';

// Convert hex to RGB
function hexToRgb(hex: string): { r: number; g: number; b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : { r: 0, g: 0, b: 0 };
}

// Convert RGB to hex
function rgbToHex(r: number, g: number, b: number): string {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

// Mix two colors
function mixColors(
  color1: { r: number; g: number; b: number },
  color2: { r: number; g: number; b: number },
  ratio: number
): { r: number; g: number; b: number } {
  return {
    r: Math.round(color1.r * (1 - ratio) + color2.r * ratio),
    g: Math.round(color1.g * (1 - ratio) + color2.g * ratio),
    b: Math.round(color1.b * (1 - ratio) + color2.b * ratio),
  };
}

// Generate Tailwind-style color variants from a hex color
function generateColorVariants(baseHex: string) {
  const baseRgb = hexToRgb(baseHex);
  const white = { r: 255, g: 255, b: 255 };
  const black = { r: 0, g: 0, b: 0 };

  // Define the mixing ratios for each variant
  const variants = {
    50: 0.95, // 95% white, 5% base color
    100: 0.9, // 90% white, 10% base color
    200: 0.75, // 75% white, 25% base color
    300: 0.6, // 60% white, 40% base color
    400: 0.3, // 30% white, 70% base color
    500: 0, // Base color (no mixing)
    600: 0.1, // 10% darker
    700: 0.2, // 20% darker
    800: 0.3, // 30% darker
    900: 0.4, // 40% darker
  };

  const colorVariants: Record<string, string> = {};

  Object.entries(variants).forEach(([variant, ratio]) => {
    let mixedColor;

    if (parseInt(variant) <= 500) {
      // For lighter variants (50-500), mix with white
      const lightRatio = parseInt(variant) === 500 ? 0 : ratio;
      mixedColor = mixColors(baseRgb, white, lightRatio);
    } else {
      // For darker variants (600-900), mix with black
      mixedColor = mixColors(baseRgb, black, ratio);
    }

    colorVariants[variant] = rgbToHex(mixedColor.r, mixedColor.g, mixedColor.b);
  });

  return colorVariants;
}

// Stage to base color mapping
const STAGE_BASE_COLORS: Record<string, string> = {
  Lead: '#3B82F6',
  Prospect: '#EF4444',
  Qualified: '#10B981',
  SQL: '#F59E0B',
  Customer: '#22C55E',
  Unqualified: '#EC4899',
  'No Show': '#14B8A6',
  Cancelled: '#F97316',
  'Closed Lost': '#6366F1',
  Rescheduled: '#84CC16',
};

// Get color variants for a stage
const getStageColorVariants = (stageName: string, customColor?: string) => {
  const baseColor = customColor || STAGE_BASE_COLORS[stageName] || '#6B7280';
  return generateColorVariants(baseColor);
};

// Get specific variant
const getColorVariant = (
  baseColor: string,
  variant: keyof ReturnType<typeof generateColorVariants>
) => {
  const variants = generateColorVariants(baseColor);
  return variants[variant];
};

// Get semantic colors for UI components
const getStageColors = (stageName: string, customColor?: string) => {
  const variants = getStageColorVariants(stageName, customColor);

  return {
    background: variants['50'],
    border: variants['200'],
    text: variants['800'],
    hover: {
      background: variants['100'],
      border: variants['300'],
    },
  };
};

const StagesContainer = ({ stageName }: { stageName?: string }) => {
  const { contactStagesOptions } = useContactStages(false);
  //const userState = useRecoilValue(UserState);

  // Safely access contactstages
  const contactStages = contactStagesOptions || [];

  const currentStage = contactStages.find(
    (stage) => stage.label.toLowerCase() === stageName?.toLowerCase()
  );

  // Get colors using the new variant system
  const colors = getStageColors(stageName || '', currentStage?.color);

  return (
    <HStack>
      <Badge
        // border="1px solid"
        // borderColor={colors.hover.border}
        bg={colors.hover.background}
        color={colors.text}
        rounded={'full'}
        px={'3'}
        fontWeight={'700'}
        //fontSize={'sm'}
        //transition="all 0.2s"
      >
        {stageName}
      </Badge>
    </HStack>
  );
};

// Export utilities for use elsewhere
export {
  generateColorVariants,
  getColorVariant,
  getStageColorVariants,
  getStageColors,
};

export default StagesContainer;
