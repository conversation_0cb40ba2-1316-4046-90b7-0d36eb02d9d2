import { Colors } from '@/constants/colors';
import { Flex, FlexProps, Text } from '@chakra-ui/react';
import React from 'react';
import { FiArrowLeft } from 'react-icons/fi';

export default function GoBack(props: FlexProps) {
  return (
    <div>
      <Flex
        gap={'1rem'}
        cursor={'pointer'}
        alignItems={'center'}
        mb={6}
        color={Colors?.ORANGE?.PRIMARY}
        {...props}
      >
        <FiArrowLeft />
        <Text fontWeight={'bold'}>Go Back</Text>
      </Flex>
    </div>
  );
}
