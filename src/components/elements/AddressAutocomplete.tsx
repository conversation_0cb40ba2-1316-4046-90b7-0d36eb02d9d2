/* eslint-disable react-hooks/exhaustive-deps */

'use client';

import { Box } from '@chakra-ui/react';
import { useEffect, useRef, useState, useCallback } from 'react';
import StringInput from '../Input/StringInput';

type AddressAutocompleteProps = {
  value?: string;
  onSelect: (address: string) => void;
  onChange: any;
  placeholder?: string;
};

export default function AddressAutocomplete({
  value,
  onSelect,
  onChange,
  placeholder = 'Enter address',
}: AddressAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const autocompleteRef = useRef<any>(null);
  const [inputValue, setInputValue] = useState(value || '');
  const isInitialized = useRef(false);

  // Memoize the onSelect callback to prevent unnecessary re-renders
  const memoizedOnSelect = useCallback(
    (address: string) => {
      onSelect(address);
    },
    [onSelect]
  );

  // Check if Google Maps is loaded
  useEffect(() => {
    const checkGoogleLoaded = () => {
      if (
        typeof window !== 'undefined' &&
        (window as any).google?.maps?.places?.Autocomplete
      ) {
        setIsGoogleLoaded(true);
      } else {
        setTimeout(checkGoogleLoaded, 100);
      }
    };

    checkGoogleLoaded();
  }, []);

  // Initialize autocomplete - only once when Google is loaded
  useEffect(() => {
    if (!isGoogleLoaded || !inputRef.current || isInitialized.current) {
      return;
    }

    const googleMaps = (window as any).google.maps;

    const autocomplete = new googleMaps.places.Autocomplete(inputRef.current, {
      types: ['geocode'],
      fields: [
        'formatted_address',
        'geometry',
        'address_components',
        'place_id',
        'name',
      ],
    });

    // Store autocomplete reference
    autocompleteRef.current = autocomplete;
    isInitialized.current = true;

    const handlePlaceChanged = () => {
      const place = autocomplete.getPlace();

      if (place && place.formatted_address) {
        setInputValue(place.formatted_address);
        memoizedOnSelect(place.formatted_address);
      } else if (place && inputRef.current?.value) {
        memoizedOnSelect(inputRef.current.value);
      } else {
        console.log('No valid place data found');
      }
    };

    autocomplete.addListener('place_changed', handlePlaceChanged);

    return () => {
      if (autocomplete) {
        googleMaps.event.clearInstanceListeners(autocomplete);
      }
      isInitialized.current = false;
    };
  }, [isGoogleLoaded]);

  // Update input value when prop changes
  useEffect(() => {
    if (value !== undefined && value !== inputValue) {
      setInputValue(value);
    }
  }, [value]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Call the original onChange handler
    if (onChange) {
      onChange(e);
    }
  };

  return (
    <Box pos={'relative'}>
      <StringInput
        ref={inputRef}
        inputProps={{
          placeholder: isGoogleLoaded ? placeholder : 'Loading Google Maps...',
          borderColor: 'gray.100',
          name: 'address',
          value: inputValue,
          disabled: !isGoogleLoaded,
          onChange: handleInputChange,
          autoComplete: 'off',
        }}
        fieldProps={{ label: 'Address' }}
      />
    </Box>
  );
}
