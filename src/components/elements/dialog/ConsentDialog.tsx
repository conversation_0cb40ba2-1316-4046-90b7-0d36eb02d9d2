import React from 'react';
import { IoInformationCircleOutline } from 'react-icons/io5';
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogRoot,
} from '@/components/ui/dialog';
import {
  Box,
  Center,
  DialogActionTrigger,
  DialogFooter,
  DialogRootProps,
  Stack,
} from '@chakra-ui/react';
import { Button } from '@/components/ui/button';

type DialogProps = Omit<DialogRootProps, 'children'>;

interface ConsentDialogProps extends DialogProps {
  w?: any;
  heading?: any;
  h?: any;
  overflow?: 'auto' | 'scroll' | 'hidden';
  isLoading?: boolean;
  note?: string;
  handleSubmit: () => void;
  firstBtnText?: string;
  secondBtnText?: string;
}

export const ConsentDialog = ({
  isLoading,
  note = `Are you sure? You can't undo this action afterwards.`,
  heading = `Delete Customer`,
  firstBtnText = 'Cancel',
  secondBtnText = 'Yes, Proceed',
  handleSubmit,
  open,
  closeOnInteractOutside,
  onOpenChange,
}: ConsentDialogProps) => {
  return (
    <DialogRoot
      open={open}
      closeOnInteractOutside={closeOnInteractOutside}
      size={'sm'}
      onOpenChange={onOpenChange}
    >
      <DialogContent>
        <DialogBody>
          <Stack alignItems={'center'} w={'100%'} mt={'8'}>
            <Center py={'2px'}>
              <IoInformationCircleOutline size={42} color="#e97a5b" />
            </Center>
            <Box fontWeight="bold" fontSize="lg" textAlign={'center'}>
              {heading}
            </Box>
            <Box textAlign={'center'}>{note}</Box>
          </Stack>
        </DialogBody>
        <DialogFooter flexDir={'column'} mb={'8'} gap={'1rem'}>
          <Button
            w={'full'}
            bg={'primary.500'}
            onClick={handleSubmit}
            loading={isLoading}
            data-no-row-click="true"
          >
            {secondBtnText}
          </Button>
          <DialogActionTrigger asChild>
            <Button data-no-row-click="true" variant="outline" w={'full'}>
              {firstBtnText}
            </Button>
          </DialogActionTrigger>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  );
};
