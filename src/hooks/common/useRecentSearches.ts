import { useEffect, useState } from 'react';

type RecentClient = {
  id: string;
  display_name: string;
  stage: string;
  primary_email: string | null;
};

const STORAGE_KEY = 'recentClientSearches';

export const useRecentSearches = () => {
  const [recentSearches, setRecentSearches] = useState<RecentClient[]>([]);

  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      setRecentSearches(JSON.parse(stored));
    }
  }, []);

  const addClientToRecent = (client: RecentClient) => {
    const stored: RecentClient[] = JSON.parse(
      localStorage.getItem(STORAGE_KEY) || '[]'
    );

    const updated = [client, ...stored.filter((c) => c.id !== client.id)].slice(
      0,
      5
    ); // max 5 items

    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    setRecentSearches(updated);
  };

  return { recentSearches, addClientToRecent };
};
