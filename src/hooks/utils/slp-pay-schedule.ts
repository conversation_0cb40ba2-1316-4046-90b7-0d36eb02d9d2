//THIS HOOKS UPDATES THE PAY SCHEDULE TABLE

import { IUser } from '@/shared/interface/user';
import { useGetInvoicesByUserQuery } from '@/api/invoices/find-by-user';
import {
  calculateTotalHoursBySLP,
  convertDate,
  filterAndCalculateTotalHours,
} from '@/utils/invoiceUtils';
import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';

export const useUtilPaySchedule = ({ slp }: { slp: IUser }) => {
  const { data: AIapi } = useGetInvoicesByUserQuery({
    id: slp?.id,
    filter: { org_id: slp?.organization_id },
  }) as any;

  const updatePaySchedule = async () => {
    if (!slp || !AIapi) {
      return;
    }
    const pay_rate = Number(slp.pay_rate);
    const arr: Array<any> = [];
    const dataInvocies = AIapi;
    const data = dataInvocies?.filter((item: any) => item.status !== 'Void');
    // console.log('All invoices are', data);
    const calculatedTotalhours = filterAndCalculateTotalHours(data);

    Object.keys(calculatedTotalhours).map((groupKey) => {
      const wholeDataInTheGroup = calculatedTotalhours[groupKey];
      const allAxInvoies = wholeDataInTheGroup?.filter(
        (item: any) => item.session_type === 'Ax'
      );
      const total_hours = calculateTotalHoursBySLP(wholeDataInTheGroup);
      const total_hours_for_Ax = calculateTotalHoursBySLP(allAxInvoies);
      const pay_period = convertDate(groupKey);
      const base_pay = total_hours * pay_rate;
      const ax_pay = parseFloat(
        (total_hours_for_Ax * pay_rate * 0.2).toFixed(2)
      );
      const axPayAvailable = new Date(groupKey) > new Date('2024-04-15');
      arr.push({
        slp_id: slp.id,
        pay_rate,
        total_hours,
        pay_period: pay_period.date,
        year: pay_period.year,
        base_pay,
        pay_period_raw: groupKey,
        ax_pay: axPayAvailable ? ax_pay : null,
        assesments: axPayAvailable ? total_hours_for_Ax : null,
        total_pay: axPayAvailable ? base_pay + ax_pay : base_pay,
        // status:"paid"
      });
    });

    await supabase.from(tableNames.pay_schedules).upsert(arr);
  };

  return {
    updatePaySchedule,
  };
};
