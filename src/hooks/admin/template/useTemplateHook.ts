import { useAddTemplateApi } from '@/api/template/add-template';
import { TFilter, useTemplateViewQuery } from '@/api/template/get-all-template';
import { toaster } from '@/components/ui/toaster';
import { FullClient } from '@/shared/interface/clients';
import { IUser } from '@/shared/interface/user';
import { replacementTags } from '@/utils/config';
import { extractUniqueTags } from '@/utils/template-helper';
import { useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import { useState } from 'react';
import * as Yup from 'yup';
type PaginationType = {
  total_count: number;
  number_of_pages: number;
  current_page: number;
  items_per_page: number;
};

export const useTemplateHook = ({
  slp,
  size = 50,
}: {
  slp?: IUser | FullClient;
  size?: number;
}) => {
  const { open: isOpen, onClose, onOpen } = useDisclosure();

  const [templateFilter, setTemplateFilter] = useState<TFilter>({
    items_per_page: size || 50,
    name: '',
    content: '',
    current_page: 1,
  });
  const [loading, setLoading] = useState({
    create: false,
  });
  const [selectedReplacementTag, setSelectedReplacementTag] = useState<
    string[]
  >([]);

  const initialValues = {
    name: '',
    content: '',
  };
  const { mutateAsync: createTemplateAsync } = useAddTemplateApi();
  // console.log('slp', slp);
  // console.log("Organization ID:", slp?.organization_id);

  const validationSchema = Yup.object({
    name: Yup.string().trim().required('Template name is required'),
    content: Yup.string().trim().required('Content is required'),
  });

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    resetForm,
  } = useFormik({
    initialValues: initialValues,
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading((prev) => {
          return {
            ...prev,
            create: true,
          };
        });

        const insert = {
          name: values.name,
          content: values.content,
          organization_id: slp?.organization_id,
        };

        await createTemplateAsync(insert);

        onClose();
        resetForm();
        // toaster.create({
        //     description: 'Template Created Successfully',
        //     type: 'success',
        // });

        // await refetch();
      } catch (error) {
        // setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading((prev) => {
          return {
            ...prev,
            create: false,
          };
        });
      }
    },
  });

  const {
    data: templateViewData,
    isLoading,
    error,
    // refetch,
  } = useTemplateViewQuery(slp?.organization_id, templateFilter);

  //console.log('templateViewData', templateViewData);
  const handleReplacementTag = (value: string) => {
    if (selectedReplacementTag.indexOf(value) < 0) {
      const newReplacementTag = selectedReplacementTag;
      newReplacementTag.push(value);
      setSelectedReplacementTag(() => [...newReplacementTag]);
    }
  };

  const replaceTags = (content: string, data: Record<string, any>): string => {
    const replacementTags = extractUniqueTags(content);
    let newContent = content;

    replacementTags.forEach((tag) => {
      // Extract key name from {{tag}} -> first_name, link, organization, etc.
      const key = tag.replace(/{{|}}/g, '');

      // Check if the key exists in the data object
      const value = data?.[key] ?? '';

      // Replace all occurrences of the tag in the content
      newContent = newContent.replaceAll(tag, value);
    });

    return newContent;
  };

  // const replaceTags = (content: string, data: any): string => {
  //   const replacementTagFn: any = {
  //     '{{first_name}}': () => data?.clients?.first_name,
  //     '{{date}}': () => data?.invoice_date,
  //   };
  //   const replacementTags = extractUniqueTags(content);
  //   let newContent = content;
  //   replacementTags.forEach((tag) => {
  //     const value = replacementTagFn?.[tag as string]();
  //     newContent = newContent.replaceAll(tag, value);
  //   });
  //   return newContent;
  // };

  return {
    data: templateViewData?.data,
    isLoading,
    error,
    templateFilter,
    setTemplateFilter,
    pagination: templateViewData?.pagination as PaginationType,
    isOpen,
    onClose,
    onOpen,
    replacementTags,
    handleReplacementTag,
    loading,
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    replaceTags,
  };
};
