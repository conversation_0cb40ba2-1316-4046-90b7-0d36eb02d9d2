import { useRecoilValue } from 'recoil';
import { UserState } from '@/store/user/user';
import { formatDateWithOrganizationFormat } from '@/utils/date-formatter';
import moment from 'moment';

/**
 * Hook to access organization's date format preferences
 * @returns Object with date format and formatting function
 */
export const useOrganizationDateFormat = () => {
  const userState = useRecoilValue(UserState);

  const dateFormat = userState?.organization?.date_format || 'MMM D, YYYY';

  /**
   * Format a date using the organization's preferred format
   * @param date - The date to format
   * @param fallbackFormat - Fallback format if organization format is not set
   * @returns Formatted date string
   */
  const formatDate = (
    date: Date | string | moment.Moment,
    fallbackFormat?: string
  ): string => {
    return formatDateWithOrganizationFormat(date, dateFormat, fallbackFormat);
  };

  /**
   * Check if organization has a complete date format configured
   * @returns boolean indicating if date format is complete
   */
  const hasCompleteFormat = (): boolean => {
    return !!dateFormat;
  };

  /**
   * Get the format string for the organization
   * @returns The moment.js format string
   */
  const getFormatString = (): string => {
    if (dateFormat) {
      return `${dateFormat}`;
    }
    return 'MMM D, YYYY';
  };

  return {
    dateFormat,
    formatDate,
    hasCompleteFormat,
    getFormatString,
  };
};
