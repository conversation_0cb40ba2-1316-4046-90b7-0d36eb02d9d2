/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unused-imports/no-unused-vars */
import {
  useGetAllTaxesQuery,
  useGetInvoiceByIdQuery,
  useUpdateInvoiceMutation,
} from '@/api/newsf/queries';
import { useGetPackageOfferingsQuery } from '@/api/package-offerings/get-package-offering-by-slp';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useDisclosure } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
// import { useFormik } from 'formik';
import { useEffect, useMemo, useState } from 'react';

export const useEditInvoice = (id: string) => {
  const { UserFromQuery } = useSupabaseSession();
  const itemDisclosure = useDisclosure();
  const deleteConsent = useDisclosure();
  const taxDisclosure = useDisclosure();
  const [targetItem, setTargetItem] = useState<any>({});
  const router = useRouter();

  const [itemToDelete, setItemToDelete] = useState<any>(null);
  const [invoiceData, setInvoiceData] = useState({
    invoiceSubTotal: 0,
    total: 0,
    amountDue: 0,
    tax: 0,
  });
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  const {
    data: Invoice,
    isFetching: InvoiceDataLoading,
    // refetch: RefetchInvoices,
  } = useGetInvoiceByIdQuery(Number(id), {
    enabled: !!id,
  });
  const { mutateAsync: UpdateInvoice, isLoading: UpdateInvoicePending } =
    useUpdateInvoiceMutation();

  const { data: TaxData } = useGetAllTaxesQuery(
    {
      user_id: Invoice?.data?.slp_id || '',
      org_id: Invoice?.data?.organization_id,
    },
    {
      enabled: !!Invoice?.data?.organization_id,
      generateItemId: true,
    }
  );

  const { data: PackageOfferings } = useGetPackageOfferingsQuery(
    {
      user_id: Invoice?.data?.slp_id || '',
      organization_id: Invoice?.data?.organization_id,
    },
    {
      enabled: !!UserFromQuery?.id,
      generateItemId: true,
    }
  );

  const { data: ServicesData } = useGetServicesQuery(
    UserFromQuery?.organization_id,
    {
      enabled:
        !!Invoice?.data?.organization_id && !!UserFromQuery?.organization_id,
      generateItemId: true,
    }
  );

  // console.log('Client', Client);
  const invoiceItemOptions = useMemo(() => {
    const serviceItemOptions =
      ServicesData?.services?.map((item: any) => ({
        label: `Service - ${item?.name}`,
        value: {
          ...item,
          type: 'service',
        },
      })) || [];

    const packageItemOptions =
      PackageOfferings?.map((item: any) => ({
        label: `Package - ${item?.name}`,
        value: {
          ...item,
          type: 'package',
        },
      })) || [];
    return [...serviceItemOptions, ...packageItemOptions];
  }, [ServicesData, PackageOfferings]);
  const updateFigures = (newItems: any[]) => {
    //console.log('newItems', newItems);
    const subTotal = newItems?.reduce((prev, item) => {
      return prev + item?.quantity * item?.price;
    }, 0);
    const totalTax = newItems?.reduce((prev, item) => {
      return prev + calculateItemTax(item);
    }, 0);

    const total = Number(subTotal) + Number(totalTax);
    const amountDue = total;
    setInvoiceData({
      amountDue: amountDue,
      invoiceSubTotal: subTotal,
      total: total,
      tax: totalTax,
    });
  };

  const handleItemClick = (item: any) => {
    // console.log('item is ', item);

    const itemFound = selectedItems?.find(
      (existingItem: any) => existingItem?.itemId == item?.itemId
    );
    if (itemFound) {
      const newItems = selectedItems?.map((existingItem: any) => {
        if (existingItem?.itemId == item?.itemId) {
          const quantity = itemFound?.quantity + 1;
          return {
            ...itemFound,
            quantity,
            amount: itemFound?.price * quantity,
          };
        }
        return existingItem;
      });
      updateFigures(newItems);
      return setSelectedItems(() => [...newItems]);
    }

    const { id, ...rest } = item;
    const newItems = [
      ...selectedItems,
      {
        ...rest,
        quantity: 1,
        amount: item?.price,
        ...(item?.type === 'service'
          ? { service_id: item.id }
          : { package_id: item?.id }),
      },
    ];
    updateFigures(newItems);
    setSelectedItems(() => {
      return [...newItems];
    });
  };

  const updateItem = (field: string, value: any, itemId: any) => {
    const newItems = selectedItems?.map((item: any) => {
      if (item?.itemId == itemId) {
        const data = {
          ...item,
          [field]: value,
        };
        return {
          ...data,
          amount:
            data?.quantity && data?.price
              ? data?.quantity * data?.price
              : data?.amount,
        };
      }
      return item;
    });
    updateFigures(newItems);
    setSelectedItems(() => [...newItems]);
  };

  const deleteItem = () => {
    if (!itemToDelete) return;
    const newItems = selectedItems?.filter((item) => {
      return item?.itemId != itemToDelete?.itemId;
    });
    updateFigures(newItems);
    setSelectedItems(() => [...newItems]);
    deleteConsent.onClose();
    setItemToDelete(null);
  };

  const calculateItemTax = (item: any) => {
    if (!Array.isArray(item.taxes) || item.taxes.length === 0) return 0;

    const subtotal = Number(item.price) * Number(item.quantity ?? 1);

    const totalTaxPercentage = item.taxes.reduce((sum: number, tax: any) => {
      const value = Number(tax?.value);
      return !isNaN(value) ? sum + value : sum;
    }, 0);

    return (totalTaxPercentage / 100) * subtotal;
  };
  const taxOptions = TaxData?.data
    ?.map((item: any) => ({
      label: `${item?.name} ${item?.value}%`,
      value: { id: item?.id, value: item?.value },
    }))
    .concat({
      label: 'Create New Tax',
      action: 'createNewTax',
      value: { id: Infinity, value: 0 },
    });

  const actions: { [key: string]: any } = {
    createNewTax: (data: any) => {
      taxDisclosure.onOpen();
      setTargetItem(data);
    },
  };

  useEffect(() => {
    if (!Invoice?.data) return;
    const items = Invoice?.data?.invoice_items?.map((item: any) => {
      return {
        ...item,
        itemId: item?.id,
        amount: item?.quantity * item?.price,
        description: item?.product_name,
        name: item?.product_name,
        isExisting: true,
        ...(item?.service_id && { type: 'service' }),
        ...(item?.package_id && { type: 'package' }),
      };
    });
    setSelectedItems(() => [...items]);
    updateFigures(items);
  }, [Invoice]);

  // Placeholder for future logic
  const handleEditInvoice = async () => {
    if (!selectedItems || !Invoice?.data?.invoice_items) return;
    const originalItems = Invoice.data.invoice_items;
    const existingItemIds = selectedItems
      .filter((item) => item.isExisting)
      .map((item) => item.id);

    const deletedItems = originalItems
      .filter((item: any) => !existingItemIds.includes(item.id))
      .map((item: any) => ({ ...item, is_deleted: true }));

    const newItems = selectedItems
      .filter((item) => !item.id)
      .map((item) => ({
        ...item,
        product_name: item?.description,
        tax_ids: item?.taxes?.map((item: any) => Number(item?.id)),
      }));

    const updatedItems = selectedItems.filter(
      (item) => item.id && item.isExisting
    );

    // console.log('newItems is ', newItems);
    // console.log('updatedItems is ', updatedItems);
    // console.log('deletedItems is ', deletedItems);
    // return;
    await UpdateInvoice({
      id: id,
      payload: {
        tax_value: invoiceData?.tax,
        newItems,
        deletedItems,
        updatedItems,
      },
    });
    router.back();
    // RefetchInvoices();
    toaster.create({
      description: ToastMessages.operationSuccess,
      type: 'success',
    });
  };

  return {
    handleEditInvoice,
    Invoice,
    invoiceData,

    invoiceItemOptions,
    selectedItems,
    setSelectedItems,
    handleItemClick,
    deleteItem,
    updateItem,
    taxOptions,
    InvoiceDataLoading,
    itemDisclosure,
    taxDisclosure,
    setTargetItem,
    targetItem,
    actions,
    UpdateInvoicePending,
    deleteConsent,
    setItemToDelete,
    itemToDelete,
    calculateItemTax,
  };
};
