'use client';

import PageLoader from '@/components/elements/loader/PageLoader';
import InvoiceForm from '@/components/form/invoice/InvoiceForm';
import { useGetInvoiceByIdQuery } from '@/api/newsf/queries';

export default function EditInvoice({ id }: any) {
  const { data: InvoiceData, isFetching: InvoiceDataLoading } =
    useGetInvoiceByIdQuery(Number(id), {
      enabled: !!id,
    });

  if (InvoiceDataLoading) {
    return <PageLoader />;
  }
  return <InvoiceForm initialData={InvoiceData} />;
}
