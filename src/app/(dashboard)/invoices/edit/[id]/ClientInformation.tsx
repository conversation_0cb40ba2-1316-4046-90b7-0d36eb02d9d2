import { Box, Flex, Text, Grid, Avatar, VStack } from '@chakra-ui/react';
import { FiMail, FiPhone } from 'react-icons/fi';
export default function ClientInformation({ invoice }: { invoice: any }) {
  return (
    <Box flex={1} shadow="sm">
      <Box p={6}>
        <Flex justify="space-between" align="center" mb={4}>
          <Text fontSize="xl" fontWeight="bold" color="gray.800">
            Client Information
          </Text>
        </Flex>
        <Grid
          templateColumns={{ base: '1fr', md: 'auto 1fr' }}
          gap={6}
          alignItems="center"
        >
          <Avatar.Root>
            <Avatar.Fallback name={invoice.client.display_name} />
          </Avatar.Root>

          <VStack align="start" gap={3}>
            <Text fontSize="2xl" fontWeight="bold" color="gray.800">
              {invoice.client.display_name}
            </Text>
            <VStack align="start" gap={2} fontSize="sm" color="gray.600">
              <Flex align="center" gap={2}>
                <FiMail />
                <Text>{invoice.client.email}</Text>
              </Flex>
              <Flex align="center" gap={2}>
                <FiPhone />
                <Text>{invoice.client.phone}</Text>
              </Flex>
            </VStack>
          </VStack>
        </Grid>
      </Box>
    </Box>
  );
}
