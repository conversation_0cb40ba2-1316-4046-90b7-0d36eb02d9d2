import React from 'react';
import { Metadata } from 'next';
import LinkInvoiceTransaction from '../newSFInvoiceFlow/LinkInvoiceTransaction';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page() {
  return (
    <div>
      <LinkInvoiceTransaction />
    </div>
  );
}
