import { useUpdatePurchaseMutation } from '@/api/newsf/queries';
import { Button } from '@/components/ui/button';
import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { Box, Stack, Text } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import React from 'react';

interface ManualRedeemProps {
  hookData: any;
}

const ManualRedeem: React.FC<ManualRedeemProps> = ({ hookData }) => {
  const { purchase, isRedeemed } = hookData;
  const queryClient = useQueryClient();

  const { mutateAsync: UpdatePurchase, isLoading: UpdatePurchasePending } =
    useUpdatePurchaseMutation();

  const handleManualRedeem = async () => {
    await UpdatePurchase({
      id: purchase.id,
      payload: {
        status: isRedeemed ? 'UNREDEEMED' : 'REDEEMED',
      },
    });
    toaster.create({
      description: ToastMessages.operationSuccess,
      type: 'success',
    });
    await queryClient.invalidateQueries({
      queryKey: ['newsf-get-invoice-by-id', purchase.invoice_id],
    });
    await queryClient.invalidateQueries({
      queryKey: ['newsf-update-purchase'],
    });
  };

  return (
    <Box p={4}>
      <Stack gap={'1rem'}>
        <Text textAlign={'center'} fontSize="lg" fontWeight="bold" mb={2}>
          {isRedeemed ? 'Undo Redemption' : 'Manual Redeem Purchase'}
        </Text>
        <Text textAlign={'center'} fontSize="sm" color="gray.600">
          {isRedeemed
            ? 'This will mark this purchase as not redeemed. Are you sure you want to undo this?'
            : 'By clicking the button below, this purchase will be marked as redeemed manually.'}
        </Text>
        <Box>
          <Button
            w={'100%'}
            onClick={handleManualRedeem}
            loading={UpdatePurchasePending}
            colorScheme="green"
          >
            {isRedeemed ? 'Unredeem' : 'Redeem'}
          </Button>
        </Box>
      </Stack>
    </Box>
  );
};

export default ManualRedeem;
