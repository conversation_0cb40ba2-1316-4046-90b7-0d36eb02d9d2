import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Badge,
  Box,
  Center,
  Flex,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react';
import moment from 'moment';
import { TLinkTransactionHook } from './_hook/useLinkInvoiceItemHook';

type LinkExistingTransactionsProp = {
  linkTransactionHook: TLinkTransactionHook;
  createPaymentDisclosure: any;
};

interface Transaction {
  id: number;
  amount: number;
  note: string;
  payment_method: string;
  transaction_date: string;
  currency_code: string;
  transaction_type: string;
  [key: string]: any;
}

const LinkExistingTransactions = ({
  linkTransactionHook,
  createPaymentDisclosure,
}: LinkExistingTransactionsProp) => {
  const {
    clientTransactions,
    selectedTransactions,
    handleTransactionSelect,
    handleSelectAll,
    formatCurrency,
    // formatDate,
    getPaymentMethodDisplay,
    getTransactionTypeColor,
    handleLinkTransactions,
    linkTransactionLoading,
  } = linkTransactionHook;

  const transactions: Transaction[] = (clientTransactions as any)?.data || [];
  const isLoading = !clientTransactions;

  if (isLoading) {
    return (
      <Center h="20rem">
        <AnimateLoader />
      </Center>
    );
  }

  if (!transactions.length) {
    return (
      <Box p={6} textAlign="center">
        <Text fontSize="lg" color="gray.500">
          No available transactions found for this client.
        </Text>
      </Box>
    );
  }

  return (
    <Box spaceY={'2rem'} p={6}>
      <Stack gap={4}>
        <Flex justify="space-between" align="center">
          <Heading size="md">Client Transactions</Heading>
          <Flex align="center" gap={2}>
            <Checkbox
              checked={selectedTransactions.length === transactions.length}
              onCheckedChange={(e) =>
                handleSelectAll(!!e.checked, transactions)
              }
            >
              Select All ({selectedTransactions.length}/{transactions.length})
            </Checkbox>
          </Flex>
        </Flex>

        <Stack gap={3}>
          {transactions.map((transaction) => (
            <Box
              key={transaction.id}
              border="1px solid"
              borderColor={
                selectedTransactions.includes(transaction.id)
                  ? 'blue.200'
                  : 'gray.200'
              }
              bg={
                selectedTransactions.includes(transaction.id)
                  ? 'blue.50'
                  : 'white'
              }
              borderRadius="md"
              p={4}
              _hover={{ borderColor: 'blue.300', shadow: 'sm' }}
              transition="all 0.2s"
            >
              <Flex gap={4} align="start">
                <Checkbox
                  checked={selectedTransactions.includes(transaction.id)}
                  onCheckedChange={(e) =>
                    handleTransactionSelect(transaction.id, !!e.checked)
                  }
                />

                <Stack flex={1} gap={2}>
                  <Flex justify="space-between" wrap="wrap" align="center">
                    <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                      {formatCurrency(
                        transaction.amount,
                        transaction.currency_code
                      )}
                    </Text>
                    <Badge
                      colorPalette={getTransactionTypeColor(
                        transaction.transaction_type
                      )}
                      variant="subtle"
                    >
                      {transaction.transaction_type?.replace(/_/g, ' ')}
                    </Badge>
                  </Flex>

                  <Flex gap={6} wrap="wrap">
                    <Stack gap={1} minW="120px">
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Payment Method
                      </Text>
                      <Text fontSize="sm" color="gray.700">
                        {getPaymentMethodDisplay(transaction.payment_method)}
                      </Text>
                    </Stack>

                    <Stack gap={1} minW="100px">
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Date
                      </Text>
                      <Text fontSize="sm" color="gray.700">
                        {moment(
                          transaction?.transaction_date.split('T')[0]
                        ).format('MMM D, YYYY')}
                        {/* {formatDate(transaction.transaction_date)} */}
                      </Text>
                    </Stack>

                    <Stack gap={1} minW="80px">
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Currency
                      </Text>
                      <Text fontSize="sm" color="gray.700">
                        {transaction.currency_code || 'USD'}
                      </Text>
                    </Stack>
                  </Flex>

                  {transaction.note && (
                    <Stack gap={1}>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Note
                      </Text>
                      <Text fontSize="sm" color="gray.700" lineClamp={2}>
                        {transaction.note}
                      </Text>
                    </Stack>
                  )}
                </Stack>
              </Flex>
            </Box>
          ))}
        </Stack>

        {selectedTransactions.length > 0 && (
          <Box
            p={4}
            bg="blue.50"
            borderRadius="md"
            border="1px solid"
            borderColor="blue.200"
          >
            <Text fontSize="sm" color="blue.700" fontWeight="medium">
              {selectedTransactions.length} transaction
              {selectedTransactions.length !== 1 ? 's' : ''} selected
            </Text>
          </Box>
        )}
      </Stack>
      <Box>
        <Button
          w={'100%'}
          type="submit"
          bg={'#e97a5b'}
          loading={linkTransactionLoading}
          disabled={linkTransactionLoading}
          onClick={() => {
            handleLinkTransactions(createPaymentDisclosure.onClose);
          }}
        >
          Link Transaction
        </Button>
      </Box>
    </Box>
  );
};

export default LinkExistingTransactions;
