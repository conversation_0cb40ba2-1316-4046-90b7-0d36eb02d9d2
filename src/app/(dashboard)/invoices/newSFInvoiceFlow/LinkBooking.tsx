import React from 'react';
import { Box, Stack, Tabs, Text } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { useLinkBookingHook } from '@/hooks/invoices/useLinkBookingHook';
import ManualRedeem from './ManualRedeem';
import moment from 'moment';
import AnimateLoader from '@/components/elements/loader/animate-loader';

interface LinkBookingProps {
  purchase?: any;
  invoice?: any;
  onBookingCreated?: (booking: any) => void;
  onBookingSelected?: (booking: any) => void;
  onClose?: () => void;
  isRedeemed?: boolean;
}

const LinkBooking: React.FC<LinkBookingProps> = ({
  purchase,
  invoice,
  onBookingCreated,
  onBookingSelected,
  onClose,
  isRedeemed,
}) => {
  const hookData = useLinkBookingHook({
    onBookingCreated,
    onBookingSelected,
    onClose,
    purchase,
    invoice,
    isRedeemed,
  });

  const {
    // State
    activeTab,
    loading,

    // Data
    servicesOption,
    SlpOptions,

    // Form
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,

    // Handlers
    handleValueChange,
  } = hookData;

  //console.log('values is ', values);

  return (
    <>
      <Text textAlign={'center'} mb={4}>
        Link a booking to this invoice
      </Text>
      <Tabs.Root
        border={'none'}
        defaultValue={'newBooking'}
        lazyMount
        size={{ base: 'sm', md: 'md' }}
        value={activeTab}
        onValueChange={handleValueChange}
      >
        <Box
          style={{ position: 'sticky', zIndex: 1, top: '80px' }}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
        >
          <Tabs.List
            display={'flex'}
            border={'none'}
            alignItems={'center'}
            gap={'6'}
            overflowY={'hidden'}
          >
            <Tabs.Trigger
              value={'newBooking'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Create New Booking
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'existingBooking'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Select Existing Booking
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'manualRedeem'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Manual Redeem
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        <Box pt={'3'} paddingRight={'2'} paddingLeft={'2'} flex={1} pb={'20'}>
          <Tabs.Content value={'newBooking'}>
            <Box>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <Stack gap={'1rem'}>
                  <CustomSelect
                    placeholder="Select Service"
                    // required={true}
                    isDisabled={true}
                    selectedOption={servicesOption?.find(
                      (item: any) => item?.value?.id === purchase?.service_id
                    )}
                    onChange={(val) => {
                      setFieldValue('event', val?.value.name);
                      setFieldValue('service_id', val?.value.id);
                    }}
                    options={servicesOption}
                    label="Service"
                    name="service_id"
                    errors={errors}
                  />
                  <CustomSelect
                    options={SlpOptions}
                    onChange={(val) => {
                      setFieldValue('assigned_to', val?.value);
                    }}
                    selectedOption={SlpOptions?.find(
                      (item: any) => item?.value === values.assigned_to
                    )}
                    label="Assigned To"
                    name="assigned_to"
                    required={true}
                    errors={errors}
                  />

                  <StringInput
                    inputProps={{
                      name: 'appointment',
                      type: 'datetime-local',
                      placeholder: 'Select Date and Time',
                      onBlur: handleBlur,
                      value: values.appointment,
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Appointment Time',
                      required: true,
                      invalid: touched.appointment && !!errors.appointment,
                      errorText: String(errors.appointment),
                    }}
                  />

                  <Button
                    // disabled={Object.keys(errors).length > 0}
                    w={'100%'}
                    type="submit"
                    loading={loading}
                  >
                    Create Booking
                  </Button>
                </Stack>
              </form>
            </Box>
          </Tabs.Content>

          <Tabs.Content value={'existingBooking'}>
            <ExistingBookingSelector hookData={hookData} />
          </Tabs.Content>
          <Tabs.Content value={'manualRedeem'}>
            <ManualRedeem hookData={hookData} />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </>
  );
};

// Component for selecting existing bookings
const ExistingBookingSelector: React.FC<{ hookData: any }> = ({ hookData }) => {
  const {
    // State
    selectedExistingBooking,
    linkableBookingsLoading,
    loading,

    // Data
    existingBookingOptions,

    // Handlers
    handleExistingBookingSelect,
    handleSubmit,
  } = hookData;
  if (linkableBookingsLoading) {
    return <AnimateLoader />;
  }

  return (
    <Box>
      <Stack gap={'1rem'}>
        <Text textAlign={'center'} fontSize="lg" fontWeight="bold" mb={2}>
          Select an existing booking to link to this invoice
        </Text>

        {existingBookingOptions.length === 0 ? (
          <Text textAlign={'center'} fontSize="sm" color="gray.600">
            No linkable bookings found for this client.
          </Text>
        ) : (
          <>
            <CustomSelect
              placeholder="Select Existing Booking"
              required={true}
              onChange={(val) => handleExistingBookingSelect(val?.value)}
              options={existingBookingOptions}
              label="Existing Bookings"
              name="existing_booking"
            />

            {selectedExistingBooking && (
              <Box p={4} bg="gray.50" borderRadius="md">
                <Text fontSize="sm" fontWeight="bold" mb={2}>
                  Selected Booking Details:
                </Text>
                {(() => {
                  const bookingOption = existingBookingOptions.find(
                    (option: any) => option.value === selectedExistingBooking
                  );
                  const booking = bookingOption?.booking;
                  return booking ? (
                    <Stack gap={1} fontSize="sm">
                      <Text>
                        <strong>Service:</strong>{' '}
                        {booking.event || 'Unknown Service'}
                      </Text>
                      <Text>
                        <strong>Date:</strong>{' '}
                        {moment(booking.appointment).format(
                          'MMM DD, YYYY h:mm A'
                        )}
                      </Text>
                      <Text>
                        <strong>Assigned To:</strong>{' '}
                        {booking.assigned_to || 'Not assigned'}
                      </Text>
                      <Text>
                        <strong>Status:</strong>{' '}
                        {booking.slp_notes
                          ? 'Has Notes (No Invoice)'
                          : 'No Notes'}
                      </Text>
                    </Stack>
                  ) : null;
                })()}
              </Box>
            )}

            <Button
              disabled={!selectedExistingBooking}
              w={'100%'}
              onClick={() => {
                handleSubmit();
              }}
              loading={loading}
            >
              Link Selected Booking
            </Button>
          </>
        )}
      </Stack>
    </Box>
  );
};

export default LinkBooking;
