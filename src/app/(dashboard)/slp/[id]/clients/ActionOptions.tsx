import React from 'react';
import {
  Box,
  MenuContent,
  MenuI<PERSON>,
  MenuR<PERSON>,
  MenuSeparator,
  MenuTrigger,
} from '@chakra-ui/react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { useActionOptionsHook } from './useActionOptionsHook';
import EditClientModal from './EditClientModal';

const ActionOptions = ({ data }: { data: any }) => {
  const {
    deleteDisclosure,
    editDisclosure,
    handleDelete,
    handleEdit,
    isDeleting,
  } = useActionOptionsHook({
    data,
  });

  return (
    <Box position={'relative'}>
      <MenuRoot>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent
          cursor={'pointer'}
          position={'absolute'}
          right={0}
          left={'auto'}
        >
          <MenuItem value="edit" onClick={handleEdit}>
            <Box>Edit</Box>
          </MenuItem>
          <MenuSeparator />
          <MenuItem value="delete" onClick={deleteDisclosure.onOpen}>
            <Box>Delete</Box>
          </MenuItem>
        </MenuContent>
      </MenuRoot>

      <ConsentDialog
        open={deleteDisclosure.open}
        onOpenChange={deleteDisclosure.onClose}
        heading="Delete Client"
        note="Are you sure you want to delete this client? This action cannot be undone."
        handleSubmit={handleDelete}
        isLoading={isDeleting}
        secondBtnText="Yes, Delete"
        firstBtnText="Cancel"
      />

      <EditClientModal
        open={editDisclosure.open}
        onClose={editDisclosure.onClose}
        data={data}
      />
    </Box>
  );
};

export default ActionOptions;
