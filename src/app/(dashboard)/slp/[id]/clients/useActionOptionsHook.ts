import { useState } from 'react';
import { useDisclosure } from '@chakra-ui/react';
import { useUpdateClientMutation } from '@/api/clients/update-client';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

interface UseActionOptionsHookProps {
  data: any; // The client data
}

export const useActionOptionsHook = ({ data }: UseActionOptionsHookProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const deleteDisclosure = useDisclosure();
  const editDisclosure = useDisclosure();
  const queryClient = useQueryClient();

  const updateClientMutation = useUpdateClientMutation({
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Client deleted successfully',
      });
      // Invalidate the SLP client tables to refresh the data
      queryClient.invalidateQueries([queryKey.users.getSlpIdView]);
      queryClient.invalidateQueries([queryKey.users.getNonInvoicedClients]);
      // Also invalidate general client queries in case they're used elsewhere
      queryClient.invalidateQueries([queryKey.client.search]);
      queryClient.invalidateQueries([queryKey.client.searchAll]);
      deleteDisclosure.onClose();
      setIsDeleting(false);
    },
    onError: (error: any) => {
      toaster.create({
        type: 'error',
        description: error?.message || 'Failed to delete client',
      });
      setIsDeleting(false);
    },
  });

  const handleDelete = async () => {
    if (!data?.id) {
      toaster.create({
        type: 'error',
        description: 'Client ID not found',
      });
      return;
    }

    setIsDeleting(true);

    try {
      await updateClientMutation.mutateAsync({
        id: data.id,
        data: {
          slp_notes: 'deleted',
        },
      });
      queryClient.invalidateQueries({
        queryKey: [queryKey.users.getSlpIdView],
        exact: false,
        refetchType: 'all',
      });
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Delete client error:', error);
    }
  };

  const handleEdit = () => {
    editDisclosure.onOpen();
  };

  return {
    deleteDisclosure,
    editDisclosure,
    handleDelete,
    handleEdit,
    isDeleting,
  };
};
