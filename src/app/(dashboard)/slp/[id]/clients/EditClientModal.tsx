import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Box, Flex, Grid, GridItem, Stack, Text } from '@chakra-ui/react';
import React from 'react';
import { Button } from '@/components/ui/button';
import { Field } from '@/components/ui/field';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';
import { useEditClientHook } from './useEditClientHook';
import { referralSourceOptions } from '@/data/options/consultations';

interface EditClientModalProps {
  open: boolean;
  onClose: () => void;
  data: any; // Client data to edit
}

export default function EditClientModal({
  open,
  onClose,
  data,
}: EditClientModalProps) {
  const { contactStagesOptions } = useContactStages(false);
  const {
    values,
    touched,
    handleFormSubmit,
    errors,
    handleChange,
    setFieldValue,
    handleFirstNameChange,
    handleLastNameChange,
    handleDisplayNameChange,
    loading,
    stateOptions,
    countryOptions,
    cityOptions,
  } = useEditClientHook({ onClose, data });

  return (
    <CustomModal
      w={{ base: '90%', md: '40rem' }}
      open={open}
      onOpenChange={onClose}
    >
      <Box my={'1rem'}>
        <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
          Edit Client
        </Text>
      </Box>

      <form onSubmit={handleFormSubmit}>
        <Stack gap={'1rem'} pt={'1rem'}>
          {/* First Name and Last Name */}
          <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap="1rem">
            <GridItem>
              <StringInput
                fieldProps={{
                  invalid: touched.first_name && !!errors.first_name,
                  label: 'First Name',
                  required: true,
                  errorText: errors.first_name,
                }}
                inputProps={{
                  name: 'first_name',
                  value: values.first_name?.trim() || '',
                  onChange: handleFirstNameChange,
                }}
              />
            </GridItem>

            <GridItem>
              <StringInput
                inputProps={{
                  name: 'last_name',
                  value: values.last_name?.trim() || '',
                  onChange: handleLastNameChange,
                }}
                fieldProps={{
                  invalid: touched.last_name && !!errors.last_name,
                  label: 'Last Name',
                  errorText: errors.last_name,
                  required: true,
                }}
              />
            </GridItem>
          </Grid>

          {/* Display Name */}
          <StringInput
            inputProps={{
              name: 'display_name',
              value: values.display_name || '',
              onChange: handleDisplayNameChange,
            }}
            fieldProps={{
              invalid: touched.display_name && !!errors.display_name,
              label: 'Customer Display Name',
              errorText: errors.display_name,
              required: true,
            }}
          />

          {/* Email */}
          <StringInput
            inputProps={{
              name: 'email',
              type: 'email',
              value: values.email || '',
              onChange: handleChange,
            }}
            fieldProps={{
              invalid: touched.email && !!errors.email,
              label: 'Email',
              errorText: errors.email,
              required: true,
            }}
          />

          {/* Phone */}
          <StringInput
            inputProps={{
              name: 'phone',
              value: values.phone || '',
              onChange: handleChange,
            }}
            fieldProps={{
              invalid: touched.phone && !!errors.phone,
              label: 'Phone',
              errorText: errors.phone,
            }}
          />

          {/* Country */}
          <CustomSelect
            onChange={(option) => {
              setFieldValue('country', option.value);
            }}
            selectedOption={countryOptions?.find(
              (option) => option?.value?.isoCode === values?.country?.isoCode
            )}
            options={countryOptions}
            label="Country"
          />

          {/* State and City */}
          <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap="1rem">
            <GridItem>
              <CustomSelect
                onChange={(option) => {
                  setFieldValue('state', option.value);
                }}
                selectedOption={stateOptions?.find(
                  (option) => option?.value?.isoCode === values?.state?.isoCode
                )}
                options={stateOptions}
                label="State/Province"
              />
            </GridItem>

            <GridItem>
              <CustomSelect
                onChange={(option) => {
                  setFieldValue('city', option.value);
                }}
                selectedOption={cityOptions?.find(
                  (option) => option?.value?.name === values?.city?.name
                )}
                options={cityOptions}
                label="City"
              />
            </GridItem>
          </Grid>

          <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap="1rem">
            <GridItem>
              {/* Status */}
              <Field
                label={'Status'}
                errorText={errors.stage}
                invalid={touched.stage && !!errors.stage}
                required={true}
              >
                <CustomSelect
                  placeholder="Select status"
                  options={contactStagesOptions}
                  onChange={(val) => setFieldValue('stage', val.value)}
                  selectedOption={contactStagesOptions?.find(
                    (item: any) =>
                      item.value.toLowerCase() === values.stage?.toLowerCase()
                  )}
                />
              </Field>
            </GridItem>

            <GridItem>
              <CustomSelect
                placeholder="Select source"
                options={referralSourceOptions}
                onChange={(val) => {
                  setFieldValue('referral_source', val.value);
                }}
                label="Referral Source"
                selectedOption={referralSourceOptions?.find(
                  (item) =>
                    item?.value?.toLowerCase() ===
                    values?.referral_source?.toLowerCase()
                )}
              />
            </GridItem>
          </Grid>

          {/* Buttons */}
          <Flex
            my={'1.8rem'}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Button
              onClick={onClose}
              variant={'outline'}
              minH={'3rem'}
              minW={'15rem'}
            >
              Cancel
            </Button>
            <Button
              loading={loading}
              minH={'3rem'}
              bg={'primary.500'}
              minW={'15rem'}
              type="submit"
            >
              Update
            </Button>
          </Flex>
        </Stack>
      </form>
    </CustomModal>
  );
}
