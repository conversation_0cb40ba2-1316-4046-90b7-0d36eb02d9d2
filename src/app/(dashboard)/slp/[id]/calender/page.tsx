import { createSupabaseServer } from '@/lib/supabase/server';
import { Metadata } from 'next';
import Calendar from './Calender';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page({ params }: { params: { id: string } }) {
  const supabase = await createSupabaseServer();
  const { data } = await supabase.rpc('get_user_by_id', {
    id_param: Number(params.id),
  });

  return (
    <div>
      <Calendar slp={data} />
    </div>
  );
}
