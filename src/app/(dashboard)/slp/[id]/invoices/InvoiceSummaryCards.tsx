import React from 'react';
import { Box, SimpleGrid, Icon, HStack, Text } from '@chakra-ui/react';
import { StatRoot, StatLabel } from '@/components/ui/stat';
import {
  MdOutlineSchedule,
  MdOutlineWarning,
  MdOutlineHistory,
} from 'react-icons/md';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { InvoiceSummaryData } from '@/api/invoices/get-summary';

interface InvoiceSummaryCardsProps {
  summaryData?: InvoiceSummaryData;
  isLoading?: boolean;
}

export default function InvoiceSummaryCards({
  summaryData,
  isLoading = false,
}: InvoiceSummaryCardsProps) {
  // Default values when data is not available
  const defaultSummaryData: InvoiceSummaryData = {
    overdue: { count: 0, total: 0 },
    dueWithin30: { count: 0, total: 0 },
    recent: { count: 0, total: 0 },
    statusCounts: { unpaid: 0, total: 0 },
  };

  const data = summaryData || defaultSummaryData;

  const summaryCards = [
    {
      label: 'Overdue Invoices',
      count: data.overdue.count,
      total: data.overdue.total,
      icon: MdOutlineWarning,
      color: 'red.500',
      bgColor: 'red.50',
      borderColor: 'red.200',
    },
    {
      label: 'Due Within 30 Days',
      count: data.dueWithin30.count,
      total: data.dueWithin30.total,
      icon: MdOutlineSchedule,
      color: 'orange.500',
      bgColor: 'orange.50',
      borderColor: 'orange.200',
    },
    {
      label: 'Last 30 Days',
      count: data.recent.count,
      total: data.recent.total,
      icon: MdOutlineHistory,
      color: 'blue.500',
      bgColor: 'blue.50',
      borderColor: 'blue.200',
    },
  ];

  return (
    <Box mb="6">
      <SimpleGrid
        columns={{ base: 1, md: 3 }}
        gap={{ base: '4', md: '6' }}
        mt="4"
      >
        {summaryCards.map((card, index) => (
          <Box
            key={index}
            p="6"
            bg={card.bgColor}
            borderWidth="1px"
            borderColor={card.borderColor}
            borderRadius="lg"
            shadow="sm"
            _hover={{ shadow: 'md' }}
            transition="all 0.2s"
            opacity={isLoading ? 0.6 : 1}
          >
            <HStack gap="4" align="start">
              <Box p="3" bg="white" borderRadius="lg" shadow="sm">
                <Icon as={card.icon} boxSize="6" color={card.color} />
              </Box>
              <Box flex="1">
                <StatRoot>
                  <Text fontSize="2xl" fontWeight="bold" color={card.color}>
                    {isLoading ? '...' : card.count}
                  </Text>
                  <StatLabel fontSize="sm" color="gray.600" fontWeight="medium">
                    {card.label}
                  </StatLabel>
                  <Text
                    fontSize="lg"
                    fontWeight="semibold"
                    color="gray.800"
                    mt="1"
                  >
                    {isLoading ? '...' : formatMoney(card.total)}
                  </Text>
                </StatRoot>
              </Box>
            </HStack>
          </Box>
        ))}
      </SimpleGrid>
    </Box>
  );
}
