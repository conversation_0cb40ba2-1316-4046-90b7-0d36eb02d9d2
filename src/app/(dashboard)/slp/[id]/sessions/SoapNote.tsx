import { useTemplateHookReturnType } from '@/app/(dashboard)/admin/template/AddTemplate';
import EmptyState from '@/components/elements/EmptyState';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import {
  Box,
  Center,
  Flex,
  Heading,
  HStack,
  Stack,
  Text,
} from '@chakra-ui/react';
import moment from 'moment/moment';
import { useRouter } from 'next/navigation';
import { FiUser } from 'react-icons/fi';
import { LuCalendar } from 'react-icons/lu';
import EditSoapNote from './EditSoapNote';

export default function SoapNote({
  // linkedClientOptions,
  LinkedClientLoading,
  // setSelectedClientId,
  selectedClientId,
  SoapNotes,
  SoapNotesLoading,
  currentClient,
  section,
  // linkedClientValue,
  // defaultLinkedClient,
  templateHook,
  // soapNoteHook,
}: {
  linkedClientOptions?: any;
  LinkedClientLoading?: any;
  setSelectedClientId?: any;
  selectedClientId?: any;
  SoapNotesLoading: any;
  SoapNotes: any;
  currentClient: any;
  defaultLinkedClient?: any;
  linkedClientValue?: any;
  section: 'client' | 'slp';
  templateHook?: useTemplateHookReturnType;
  soapNoteHook: TCreateInvoiceHook;
}) {
  // const path = usePathname();
  // const slpId = path.split('/')[2];
  const router = useRouter();
  const isSlp = section === 'slp';
  const isClient = section === 'client';
  // console.log('linkedClientValue is ', linkedClientValue);
  // console.log('defaultLinkedClient is ', defaultLinkedClient);

  // const {
  //   isLoading,
  //   SNapi,
  //   options,
  //   setSelectedClientId,
  //   getSlpName,
  //   selectedClientId,
  //   LCloading,
  // } = useSoapNote({
  //   client: client as any,
  //   slpId: editProfileSoap || createProfileSoap ? client?.active_slp : slpId,
  // });
  // const { booking } = soapNoteHook;

  const handleViewMoreClick = () => {
    router.push(`/contacts/${selectedClientId || currentClient?.id}#SOAP`);
  };

  console.log('SoapNotesLoading', SoapNotesLoading);
  console.log('LinkedClientLoading', LinkedClientLoading);

  if (SoapNotesLoading || LinkedClientLoading) {
    return (
      <Center w={'100%'} h={'20rem'}>
        <AnimateLoader />
      </Center>
    );
  }

  return (
    <Box w={'100%'}>
      {/* <Text textAlign={'center'} fontWeight={'600'} my={'1rem'}> */}
      {/* {booking &&
        booking?.linked_clients &&
        booking?.linked_clients?.display_name
          ? booking?.linked_clients?.display_name
          : null}{' '} */}
      {/* </Text> */}
      <Heading mb={'4'} fontSize={{ base: '1.3rem', md: '1.5rem' }}>
        Session Notes
      </Heading>

      <Stack w={'100%'}>
        {/* {linkedClientOptions?.length > 1 && (
          <Box my={'1rem'}>
            <CustomSelect
              key={linkedClientValue}
              placeholder="Select one"
              options={linkedClientOptions}
              onChange={(val) => {
                if (Number(val.value) === Number(currentClient?.id)) {
                  setSelectedClientId(null);
                  return;
                }
                setSelectedClientId(val.value);
              }}
              label="Linked clients"
              defaultValue={defaultLinkedClient}
              value={linkedClientValue}
            />
          </Box>
        )} */}
        {SoapNotes?.length > 0 ? (
          SoapNotes?.sort(
            (a: any, b: any) =>
              new Date(b.note_date).getTime() - new Date(a.note_date).getTime()
          )
            .slice(0, isSlp ? 5 : undefined)
            .map((item: any) => (
              <Box
                key={item.created_at}
                borderRadius={'md'}
                border="1px solid"
                borderColor={'gray.50'}
                p={'5'}
              >
                <Box pb={'3.5'}>
                  <Box
                    display={'flex'}
                    flexDir={'column'}
                    gap={'.3rem'}
                    fontSize={'md'}
                    //fontWeight={'500'}
                    //color={'gray.700'}
                    letterSpacing={'0.6px'}
                    className="noteBox"
                    dangerouslySetInnerHTML={{ __html: item.soap_note }}
                  ></Box>
                </Box>
                <HStack
                  pt={'3.5'}
                  borderTop="1px solid"
                  borderColor={'gray.50'}
                  justifyContent={'space-between'}
                  gap={'3'}
                >
                  <Flex alignItems={'center'} gap={'3'}>
                    <Flex
                      alignItems={'center'}
                      bg="#F3F4F6"
                      gap={'1.5'}
                      color={'gray.500'}
                      px={'2.5'}
                      rounded={'full'}
                      py={'1'}
                    >
                      <LuCalendar size={15} />
                      <Text fontSize={'sm'} fontWeight={'600'}>
                        {moment(item.note_date).format('MMM D, YYYY')}
                      </Text>
                    </Flex>
                    <Flex
                      alignItems={'center'}
                      bg={'primary.50'}
                      gap={'1.5'}
                      color={'primary.600'}
                      px={'2.5'}
                      rounded={'full'}
                      py={'1'}
                    >
                      <FiUser size={15} />
                      <Text
                        fontSize={'sm'}
                        fontWeight={'600'}
                      >{`${item?.slp_id?.first_name} ${item?.slp_id?.last_name}`}</Text>
                    </Flex>
                  </Flex>
                  {isClient && (
                    <EditSoapNote templateHook={templateHook} slpNote={item} />
                  )}
                </HStack>
              </Box>
            ))
        ) : (
          <EmptyState text="No Notes" />
        )}
        {isSlp && SoapNotes?.length >= 5 && (
          <Box onClick={handleViewMoreClick} cursor={'pointer'}>
            <Text color={'#e9485b'}>View More</Text>
          </Box>
        )}
      </Stack>
    </Box>
  );
}
