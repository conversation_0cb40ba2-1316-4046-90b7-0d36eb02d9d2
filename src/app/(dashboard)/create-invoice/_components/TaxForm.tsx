'use client';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import React from 'react';
import useTaxHook from '../_hook/useTaxHook';

interface TaxFormProps {
  onClose: () => void;
  onSuccess?: (e?: any) => void;
}

const TaxForm: React.FC<TaxFormProps> = ({ onClose, onSuccess }) => {
  const { formData, handleInputChange, handleSubmit, errors, isLoading } =
    useTaxHook({ onClose, onSuccess });

  return (
    <Box p={4}>
      <form onSubmit={handleSubmit}>
        <Stack gap={4}>
          {/* Tax Name */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Tax name{' '}
              <Text as="span" color="red.500">
                *
              </Text>
            </Text>
            <StringInput
              inputProps={{
                value: formData.name,
                onChange: (e) => handleInputChange('name', e.target.value),
                placeholder: 'Enter tax name (e.g., HST, GST, VAT)',
                border: errors.name ? '1px solid red' : '1px solid #C1D6EF',
              }}
              fieldProps={{
                errorText: errors.name,
              }}
            />
          </Box>

          {/* Description */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Description
            </Text>
            <StringInput
              inputProps={{
                value: formData.description,
                onChange: (e) =>
                  handleInputChange('description', e.target.value),
                placeholder: 'Enter tax description (optional)',
                border: '1px solid #C1D6EF',
              }}
            />
          </Box>

          {/* Tax Rate */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Tax rate{' '}
              <Text as="span" color="red.500">
                *
              </Text>
            </Text>
            <Box position="relative">
              <StringInput
                inputProps={{
                  value: formData.value,
                  onChange: (e) => handleInputChange('value', e.target.value),
                  placeholder: '0.00',
                  type: 'number',
                  step: '0.01',
                  min: '0',
                  max: '100',
                  border: errors.value ? '1px solid red' : '1px solid #C1D6EF',
                  paddingRight: '2rem',
                }}
                fieldProps={{
                  errorText: errors.value,
                }}
              />
              <Text
                position="absolute"
                right="0.75rem"
                top="50%"
                transform="translateY(-50%)"
                color="gray.500"
                fontSize="sm"
                pointerEvents="none"
              >
                %
              </Text>
            </Box>
          </Box>

          {/* Form Actions */}
          <Flex gap={3} justify="flex-end" mt={6}>
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={isLoading}
              bg="#e97a5b"
              color="white"
              _hover={{ bg: '#d16a4a' }}
            >
              Save
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default TaxForm;
