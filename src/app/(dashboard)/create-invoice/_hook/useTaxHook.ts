import {
  useCreateTaxMutation,
  useDeleteTaxMutation,
  useUpdateTaxMutation,
} from '@/api/newsf/queries';
import { toaster } from '@/components/ui/toaster';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
// import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useGetAllTaxesQuery } from '@/api/newsf/queries';
import { useSearchParams } from 'next/navigation';
import { useDisclosure } from '@chakra-ui/react';

interface TaxFormData {
  name: string;
  description: string;
  value: string;
  id?: number;
}

const initialFormData = {
  name: '',
  description: '',
  value: '',
  id: undefined,
};

const useTaxHook = ({ onClose, onSuccess }: any) => {
  //   const queryClient = useQueryClient();

  const {
    open: isDeleteOpen,
    onClose: onDeleteClose,
    onOpen: onDeleteOpen,
  } = useDisclosure();

  const { UserFromQuery } = useSupabaseSession();
  const searchParams = useSearchParams();
  const orgIdFromUrl = searchParams.get('organization_id');
  const userIdFromUrl = searchParams.get('user_id');

  const {
    data: taxData,
    isLoading: taxLoading,
    refetch: refetchTax,
  } = useGetAllTaxesQuery(
    {
      user_id: userIdFromUrl || UserFromQuery?.id || '',
      org_id: orgIdFromUrl || UserFromQuery?.organization?.id,
    },
    {
      enabled: !!userIdFromUrl || !!UserFromQuery?.organization_id,
      generateItemId: true,
    }
  );

  const [formData, setFormData] = useState<TaxFormData>(initialFormData);

  const [errors, setErrors] = useState<Partial<TaxFormData>>({});

  const { mutateAsync: createTax, isLoading } = useCreateTaxMutation();
  const { mutateAsync: updateTax, isLoading: updateLoading } =
    useUpdateTaxMutation();
  const { mutateAsync: deleteTax, isLoading: deleteLoading } =
    useDeleteTaxMutation();

  const validateForm = (): boolean => {
    const newErrors: Partial<TaxFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tax name is required';
    }

    if (!formData.value.trim()) {
      newErrors.value = 'Tax rate is required';
    } else {
      const rate = parseFloat(formData.value);
      if (isNaN(rate) || rate < 0 || rate > 100) {
        newErrors.value = 'Tax rate must be a number between 0 and 100';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof TaxFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  //console.log('formData', formData);
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (formData.id) {
        await updateTax({
          id: formData.id,
          payload: {
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            value: parseFloat(formData.value),
          },
          org_id: Number(orgIdFromUrl) || UserFromQuery?.organization_id,
        });
        toaster.create({
          type: 'success',
          description: 'Tax updated successfully',
        });
      } else {
        const result = await createTax({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          value: parseFloat(formData.value),
          organization_id:
            Number(orgIdFromUrl) || UserFromQuery?.organization_id,
          user_id: Number(userIdFromUrl) || UserFromQuery?.id,
          is_active: true,
        });
        onSuccess?.(result?.data);
        toaster.create({
          type: 'success',
          description: 'Tax created successfully',
        });
      }

      // Invalidate tax queries to refresh the list
      // queryClient.invalidateQueries(['newsf-get-all-tax']);
      setFormData(initialFormData);
      refetchTax();
      onClose?.();
    } catch (error) {
      console.error('Error creating tax:', error);
    }
  };

  const handleDelete = async (taxId: any) => {
    try {
      await deleteTax({
        id: taxId,
        org_id: Number(orgIdFromUrl) || UserFromQuery?.organization_id,
      });
      setFormData(initialFormData);
      onDeleteClose();
      refetchTax();
      toaster.create({
        type: 'success',
        description: 'Tax deleted successfully',
      });
    } catch (error: any) {
      console.log('error', error);
      toaster.create({
        description: error?.message || 'Something went wrong.',
        type: 'error',
      });
    }
  };
  return {
    formData,
    handleInputChange,
    handleSubmit,
    errors,
    isLoading,
    taxData,
    taxLoading,
    refetchTax,
    updateTax,
    updateLoading,
    deleteTax,
    deleteLoading,
    handleDelete,
    setFormData,
    isDeleteOpen,
    onDeleteClose,
    onDeleteOpen,
  };
};

export default useTaxHook;
