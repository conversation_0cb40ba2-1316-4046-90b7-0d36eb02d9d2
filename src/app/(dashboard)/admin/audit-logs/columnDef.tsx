import { createColumnHelper } from '@tanstack/react-table';
import { Box, Flex } from '@chakra-ui/react';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment-timezone';

const columnHelper = createColumnHelper<any>();

export const columnDef = [
  columnHelper.display({
    id: 'date',
    cell: (props) => (
      <Box>
        {moment(props.row.original?.created_at).format('Do MMM, YYYY, h:mm A')}
      </Box>
    ),
    header: 'Date',
  }),

  columnHelper.display({
    cell: (props) => (
      <Flex>{`${props.row.original?.users?.first_name} ${props.row.original?.users?.last_name}`}</Flex>
    ),
    header: 'User',
    id: 'user',
  }),
  columnHelper.display({
    cell: (props) => <Flex>{`${props.row.original?.resource_id} `}</Flex>,
    header: 'Resource Id',
    id: 'resource-id',
  }),
  columnHelper.display({
    cell: (props) => <Flex>{`${props.row.original?.resource_name} `}</Flex>,
    header: 'Resource Name',
    id: 'resource-name',
  }),
  columnHelper.display({
    cell: (props) => <Flex>{`${props.row.original?.action} `}</Flex>,
    header: 'Action',
    id: 'action',
  }),
];
