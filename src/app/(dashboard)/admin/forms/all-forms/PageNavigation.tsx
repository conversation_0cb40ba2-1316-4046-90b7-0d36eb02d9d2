import { <PERSON><PERSON>, <PERSON>, Button, Flex, Stack, Text } from '@chakra-ui/react';
import { FiFileText } from 'react-icons/fi';
import {
  MdAdd,
  MdNavigateBefore,
  MdNavigateNext,
  MdRemove,
} from 'react-icons/md';

interface PageNavigationProps {
  currentPage: number;
  totalPages: number;
  onAddPage: () => void;
  onRemovePage: (pageNumber: number) => void;
  onSwitchPage: (pageNumber: number) => void;
  getQuestionsForPage: (pageNumber: number) => any[];
  getPageSummary: () => Array<{
    page: number;
    questionCount: number;
    hasRequired: boolean;
  }>;
}

const PageNavigation: React.FC<PageNavigationProps> = ({
  currentPage,
  totalPages,
  onAddPage,
  onRemovePage,
  onSwitchPage,
  getQuestionsForPage,
  getPageSummary,
}) => {
  const pageSummary = getPageSummary();

  // console.log('pageSummary', pageSummary);
  // console.log('totalpages', totalPages);
  // console.log('getQuestionsForPage', getQuestionsForPage);
  // console.log('currentPage', currentPage);

  return (
    <Box
      bg="gray.50"
      p={4}
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      mb={4}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="600" color="gray.700">
          Form Pages
        </Text>
        <Flex gap={2}>
          <Button
            onClick={onAddPage}
            size="sm"
            colorScheme="blue"
            variant="outline"
            ml={2}
          >
            <MdAdd /> Add Page
          </Button>
          {totalPages > 1 && currentPage > 1 && (
            <Button
              onClick={() => onRemovePage(currentPage)}
              size="sm"
              colorScheme="red"
              variant="outline"
            >
              <MdRemove /> Remove Page {currentPage}
            </Button>
          )}
        </Flex>
      </Flex>

      {/* Page Tabs */}
      <Stack direction="row" spaceY={2} wrap="wrap" mb={4}>
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => {
          const pageInfo = pageSummary.find((p) => p.page === pageNum);
          const isActive = pageNum === currentPage;

          return (
            <Button
              key={pageNum}
              onClick={() => onSwitchPage(pageNum)}
              size="sm"
              variant={isActive ? 'solid' : 'outline'}
              colorScheme={isActive ? 'blue' : 'gray'}
              position="relative"
            >
              Page {pageNum}
              {pageInfo && (
                <Badge
                  position="absolute"
                  top="-8px"
                  right="-8px"
                  colorScheme={pageInfo.hasRequired ? 'red' : 'green'}
                  fontSize="xs"
                  borderRadius="full"
                >
                  {pageInfo.questionCount}
                </Badge>
              )}
              <FiFileText />{' '}
            </Button>
          );
        })}
      </Stack>

      {/* Navigation Controls */}
      <Flex justify="space-between" align="center">
        <Button
          onClick={() => onSwitchPage(currentPage - 1)}
          disabled={currentPage === 1}
          size="sm"
          variant="ghost"
        >
          <MdNavigateBefore /> Previous
        </Button>

        <Text fontSize="sm" color="gray.600">
          Page {currentPage} of {totalPages}
        </Text>

        <Button
          onClick={() => onSwitchPage(currentPage + 1)}
          disabled={currentPage === totalPages}
          size="sm"
          variant="ghost"
        >
          <MdNavigateNext /> Next
        </Button>
      </Flex>

      {/* Current Page Info */}
      <Box
        mt={4}
        p={3}
        bg="white"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
      >
        <Text fontSize="sm" fontWeight="500" mb={2}>
          Current Page: {currentPage}
        </Text>
        <Text fontSize="xs" color="gray.600">
          Questions: {getQuestionsForPage(currentPage).length}
          {getQuestionsForPage(currentPage).some((q) => q.required) && (
            <Badge ml={2} colorScheme="red" fontSize="xs">
              Has Required
            </Badge>
          )}
        </Text>
      </Box>
    </Box>
  );
};

export default PageNavigation;
