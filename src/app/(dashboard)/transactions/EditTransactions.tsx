import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { useDisclosure } from '@chakra-ui/react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import EditTransactionModal from './EditTransactionModal';
const EditTransactions = ({ row }: { row: any }) => {
  const { open, onClose, onOpen } = useDisclosure();

  return (
    <div>
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent cursor={'pointer'}>
          <MenuItem value="edit" cursor={'pointer'} onClick={onOpen}>
            Edit
          </MenuItem>
        </MenuContent>
      </MenuRoot>

      {open && (
        <EditTransactionModal
          isOpen={open}
          onClose={onClose}
          transaction={row}
        />
      )}
    </div>
  );
};

export default EditTransactions;
