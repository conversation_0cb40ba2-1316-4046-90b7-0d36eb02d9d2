'use client';
import { useGetClientsHook } from '@/app/(dashboard)/contacts/useAllClients';
import { Checkbox } from '@/components/ui/checkbox';

import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import {
  Badge,
  Box,
  Flex,
  HStack,
  SimpleGrid,
  Text,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { MdClear, MdFilterList } from 'react-icons/md';

type GetAllClientsHookReturnType = ReturnType<typeof useGetClientsHook>;

interface FilterModalProps {
  getClientHook: GetAllClientsHookReturnType;
}

// Define the pending filters type based on the filter state
interface PendingFiltersState {
  province: (string | number)[];
  stage: (string | number)[];
  lead_quality: (string | number)[];
  slp: (string | number)[];
  goal: (string | number)[];
  group: (string | number)[];
  active_clients: (string | number)[];
  consulted_by: (string | number)[];
  referral_source: (string | number)[];
}

export default function FilterModal({ getClientHook }: FilterModalProps) {
  const { open, onClose, onOpen } = useDisclosure();

  // Local state to hold pending filter changes
  const [pendingFilters, setPendingFilters] = useState<PendingFiltersState>({
    province: [],
    stage: [],
    lead_quality: [],
    slp: [],
    goal: [],
    group: [],
    active_clients: [],
    consulted_by: [],
    referral_source: [],
  });

  // Initialize pending filters when modal opens
  useEffect(() => {
    if (open && getClientHook?.filtersData) {
      const currentFilters: PendingFiltersState = {
        province: [...getClientHook.filter.provinceFilter],
        stage: [...getClientHook.filter.stageFilter],
        lead_quality: [...getClientHook.filter.leadFilter],
        slp: [...getClientHook.filter.slpFilter],
        goal: [...getClientHook.filter.goal],
        group: [...getClientHook.filter.group],
        active_clients: [...getClientHook.filter.active_clients],
        consulted_by: [...getClientHook.filter.consulted_by],
        referral_source: [...getClientHook.filter.referral_source],
      };
      setPendingFilters(currentFilters);
    }
  }, [open, getClientHook?.filter, getClientHook?.filtersData]);

  // Count total active filters from pending state
  const totalActiveFilters = Object.values(pendingFilters).reduce(
    (total: number, selected: (string | number)[]) =>
      total + (selected?.length || 0),
    0
  );

  const handleApplyFilters = () => {
    // Apply all pending filters to the actual filter state
    getClientHook.setFilter((prev) => ({
      ...prev,
      provinceFilter: pendingFilters.province as string[],
      stageFilter: pendingFilters.stage as string[],
      leadFilter: pendingFilters.lead_quality as string[],
      slpFilter: pendingFilters.slp as number[],
      goal: pendingFilters.goal as string[],
      group: pendingFilters.group as string[],
      active_clients: pendingFilters.active_clients as string[],
      consulted_by: pendingFilters.consulted_by as string[],
      referral_source: pendingFilters.referral_source as string[],
      currentPage: 1,
    }));
    onClose();
  };

  const handleClearAllFilters = () => {
    // Clear all pending filters
    setPendingFilters({
      province: [],
      stage: [],
      lead_quality: [],
      slp: [],
      goal: [],
      group: [],
      active_clients: [],
      consulted_by: [],
      referral_source: [],
    });
  };

  const handleLocalFilterChange = (
    checked: boolean,
    filterType: keyof PendingFiltersState,
    value: string | number
  ) => {
    // console.log('Filter change:', { checked, filterType, value }); // Debug log

    setPendingFilters((prev) => {
      const currentSelected = [...(prev[filterType] || [])];
      //   console.log('Current selected before:', currentSelected); // Debug log

      if (checked) {
        // Add if not already present
        const isAlreadySelected = currentSelected.some(
          (item) => String(item) === String(value)
        );

        if (!isAlreadySelected) {
          const newSelected = [...currentSelected, value];
          //   console.log('Adding, new selected:', newSelected); // Debug log
          return {
            ...prev,
            [filterType]: newSelected,
          };
        }
        return prev;
      } else {
        // Remove the item
        const newSelected = currentSelected.filter(
          (item: string | number) => String(item) !== String(value)
        );
        // console.log('Removing, new selected:', newSelected); // Debug log

        return {
          ...prev,
          [filterType]: newSelected,
        };
      }
    });
  };

  const handleSelectAll = (
    filterType: keyof PendingFiltersState,
    options: Array<{ value: string | number; label: string }>
  ) => {
    const allValues = options.map((option) => option.value);
    const currentSelected = pendingFilters[filterType] || [];

    // If all options are already selected, deselect all; otherwise, select all
    const areAllSelected = allValues.every((val) =>
      currentSelected.some((selected) => String(selected) === String(val))
    );

    if (areAllSelected) {
      setPendingFilters((prev) => ({
        ...prev,
        [filterType]: [],
      }));
    } else {
      setPendingFilters((prev) => ({
        ...prev,
        [filterType]: allValues,
      }));
    }
  };

  // Helper function to map filter types to pending filter keys
  const getFilterKey = (filterType: string): keyof PendingFiltersState => {
    const filterKeyMap: { [key: string]: keyof PendingFiltersState } = {
      province: 'province',
      stage: 'stage',
      lead_quality: 'lead_quality',
      slp: 'slp',
      goal: 'goal',
      group: 'group',
      active_clients: 'active_clients',
      consulted_by: 'consulted_by',
      referral_source: 'referral_source',
    };
    return filterKeyMap[filterType] || 'province';
  };

  // Helper function to check if a value is selected
  const isSelected = (
    filterType: keyof PendingFiltersState,
    value: string | number
  ): boolean => {
    const selectedValues = pendingFilters[filterType] || [];
    // Use string comparison to handle mixed types
    return selectedValues.some(
      (selected) => String(selected) === String(value)
    );
  };

  // Create updated filters data with pending selections
  const updatedFiltersData =
    getClientHook?.filtersData?.map((filter) => ({
      ...filter,
      selected: pendingFilters[getFilterKey(filter.id)] || [],
    })) || [];

  console.log('updatedFiltersData', updatedFiltersData);

  return (
    <>
      <Button
        onClick={onOpen}
        variant={'outline'}
        _hover={{ color: 'white' }}
        borderColor={'primary.500'}
        color="primary.500"
        size={'sm'}
        minW={'fit-content'}
      >
        <MdFilterList /> Filters
      </Button>

      <CustomModal
        w={{ base: '90%', md: '60rem' }}
        open={open}
        onOpenChange={onClose}
      >
        <Box>
          <HStack spaceX={3}>
            <MdFilterList size={20} />
            <Text fontSize="lg" fontWeight="600">
              Filter Contacts
            </Text>
            {totalActiveFilters > 0 && (
              <Badge bg={'primary.500'} color={'white'} rounded="full" px={2}>
                {totalActiveFilters} active
              </Badge>
            )}
          </HStack>
          <VStack spaceY={6} align="stretch" mt={4}>
            {updatedFiltersData?.map((filterGroup) => (
              <Box key={filterGroup.id}>
                <Flex justifyContent="space-between" alignItems="center" mb={4}>
                  <Text fontSize="md" fontWeight="600" color="gray.700">
                    {filterGroup.name}
                  </Text>
                  <HStack spaceX={2}>
                    {filterGroup.selected?.length > 0 && (
                      <Badge
                        bg={'primary.500'}
                        color={'white'}
                        rounded="full"
                        px={2}
                        fontSize="xs"
                      >
                        {filterGroup.selected.length} selected
                      </Badge>
                    )}
                    <Button
                      size="xs"
                      variant="outline"
                      color={'primary.500'}
                      _hover={{
                        bg: 'transparent',
                        color: 'black',
                      }}
                      borderColor={'primary.500'}
                      onClick={() =>
                        handleSelectAll(
                          getFilterKey(filterGroup.id),
                          filterGroup.options || []
                        )
                      }
                    >
                      {/* Check if all options are selected */}
                      {filterGroup.options &&
                      filterGroup.options.every((option) =>
                        isSelected(getFilterKey(filterGroup.id), option.value)
                      )
                        ? 'Deselect All'
                        : 'Select All'}
                    </Button>
                  </HStack>
                </Flex>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={2}>
                  {filterGroup?.options?.map((option) => {
                    const filterKey = getFilterKey(filterGroup.id);
                    const isOptionSelected = isSelected(
                      filterKey,
                      option.value
                    );

                    return (
                      <Box
                        key={`${option.value}-${option.label}`}
                        p={3}
                        border="1px solid"
                        // mb={2}
                        borderColor={
                          isOptionSelected ? 'blue.200' : 'primary.50'
                        }
                        bg={isOptionSelected ? 'blue.50' : 'transparent'}
                        rounded="lg"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{
                          borderColor: 'primary.300',
                          bg: 'primary.50',
                        }}
                        onClick={(e) => {
                          // Prevent the box click if the checkbox was clicked
                          if (
                            (e.target as HTMLElement).closest(
                              '[role="checkbox"]'
                            )
                          ) {
                            return;
                          }

                          //   console.log('Box clicked:', {
                          //     current: isOptionSelected,
                          //     value: option.value,
                          //   }); // Debug log
                          handleLocalFilterChange(
                            !isOptionSelected,
                            filterKey,
                            option.value
                          );
                        }}
                      >
                        <HStack spaceX={3}>
                          <Checkbox
                            checked={isOptionSelected}
                            onCheckedChange={(checked) => {
                              //   console.log('Checkbox clicked:', {
                              //     checked,
                              //     value: option.value,
                              //   }); // Debug log
                              handleLocalFilterChange(
                                !!checked,
                                filterKey,
                                option.value
                              );
                            }}
                          />
                          <Text fontSize="sm" flex={1}>
                            {option.label}
                          </Text>
                        </HStack>
                      </Box>
                    );
                  })}
                </SimpleGrid>

                {/* {index < updatedFiltersData.length - 1 && (
                  <Box h="1px" bg="gray.100" my={4} />
                )} */}
              </Box>
            ))}

            {updatedFiltersData?.length === 0 && (
              <Box textAlign="center" py={8} color="gray.500">
                <Text>No filters available</Text>
              </Box>
            )}
          </VStack>

          <Box position="sticky" bottom={0} bg="white" pt={4} pb={2} mt={6}>
            <HStack width="100%">
              <Button
                variant="outline"
                onClick={handleClearAllFilters}
                disabled={totalActiveFilters === 0}
              >
                <MdClear /> Clear All
              </Button>
              <Box flex={1} />
              <Button
                variant="outline"
                color={'primary.500'}
                _hover={{
                  bg: 'transparent',
                  color: 'black',
                }}
                borderColor={'primary.500'}
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                bg={'primary.500'}
                color={'white'}
                onClick={handleApplyFilters}
                minW="100px"
              >
                Apply Filters
              </Button>
            </HStack>
          </Box>
        </Box>
      </CustomModal>
    </>
  );
}
