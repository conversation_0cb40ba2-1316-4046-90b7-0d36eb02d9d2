import { <PERSON>, Button, Flex, Heading, Text } from '@chakra-ui/react';
import moment from 'moment';
import { BsBack } from 'react-icons/bs';

interface AnswerDetail {
  qt?: string;
  ans?: string | number | any[] | null;
  id?: string;
  page?: number;
  show_answers?: string;
}

interface AnswersProps {
  answer: {
    id: string;
    form_title?: string;
    created_at: string;
    answer_details: AnswerDetail[];
  };
  onBack: () => void;
}

const Answers = ({ answer, onBack }: AnswersProps) => {
  return (
    <Box width="100%">
      {/* Header with back button */}
      <Flex align="center" mb={6} justify="space-between">
        <Button variant="ghost" onClick={onBack} colorScheme="orange" size="sm">
          <BsBack /> Back to forms
        </Button>
        <Text fontSize="sm" color="gray.500">
          Submitted: {moment(answer.created_at).format('MMM D, YYYY h:mma')}
        </Text>
      </Flex>

      {/* Form title */}
      <Heading size="lg" mb={6}>
        {answer.form_title || 'Form Answers'}
      </Heading>

      {/* Answers list */}
      <Box
        border={'1px solid'}
        borderRadius="md"
        borderColor="gray.50"
        overflow="hidden"
        bg="white"
      >
        {answer.answer_details.map((detail, index) => (
          <Box key={detail.id || index}>
            <Box p={6}>
              <Flex direction="column" gap={2}>
                <Text fontSize="sm" color="gray.500">
                  Question {index + 1}
                </Text>
                <Text fontWeight="semibold" fontSize="lg">
                  {detail.qt || 'Untitled question'}
                </Text>

                <Box p={4} bg="gray.50" borderRadius="md" mt={2}>
                  <Text fontWeight="medium" color="gray.600" mb={1}>
                    Answer:
                  </Text>
                  <Text>
                    {!detail.ans
                      ? 'No answer provided'
                      : Array.isArray(detail.ans)
                        ? detail.ans.join(', ')
                        : String(detail.ans)}
                  </Text>
                </Box>
              </Flex>
            </Box>
            {index < answer.answer_details.length - 1 && '-'}
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default Answers;
