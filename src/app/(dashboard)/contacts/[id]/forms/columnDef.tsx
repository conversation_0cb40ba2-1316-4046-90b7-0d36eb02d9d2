import { Box, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';

const columnHelper = createColumnHelper<any>();

export const columnDef = [
  columnHelper.accessor('created_dt', {
    cell: (info) => (
      <Box>
        {moment(info.getValue())
          ? moment(info.getValue()).format('MMM D, YYYY h:mma')
          : 'N/A'}
      </Box>
    ),
    header: 'Updated At',
    id: 'booking-datetime',
  }),

  columnHelper.accessor('form_title', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Title',
    id: 'event',
  }),

  columnHelper.accessor('is_complete', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Completed',
    id: 'is-complete',
  }),

  columnHelper.accessor('updated_at', {
    cell: (info) => (
      <Box>
        {moment(info.getValue())
          ? moment(info.getValue()).format('MMM D, YYYY h:mma')
          : 'N/A'}
      </Box>
    ),
    header: 'Updated At',
    id: 'booking-datetime',
  }),

  columnHelper.accessor('form_answers', {
    cell: (info) => {
      const formAnswers = info.getValue();
      if (!formAnswers || formAnswers.length === 0) {
        return (
          <Box textAlign="right" fontSize="12px">
            No questions
          </Box>
        );
      }

      // Assuming the first answer object contains all questions
      const answerDetails = formAnswers[0]?.answer_details || [];
      const numOfQuestions = answerDetails.length;
      const numOfAnswered = answerDetails.filter(
        (answer: any) => answer.answer
      ).length;

      return (
        <Box textAlign="right" fontSize="12px">
          <Text fontWeight="500">
            {numOfQuestions} question{numOfQuestions !== 1 ? 's' : ''}
          </Text>
          <Text color="#7C7C7C">
            {numOfAnswered}/{numOfQuestions} completed
          </Text>
        </Box>
      );
    },
    header: 'Progress',
    id: 'form-progress',
  }),
];
