import EmptyState from '@/components/elements/EmptyState';
import { Colors } from '@/constants/colors';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import moment from 'moment';
import { useState } from 'react';
import { <PERSON>a<PERSON><PERSON>ck, FaExclamation } from 'react-icons/fa';
import Answers from './Answers';

interface AnswerDetail {
  ans?: string | any[] | null;
  qt?: string;
  id?: string;
  page?: number;
  show_answers?: string;
}

interface FormAnswer {
  id: string;
  form_title?: string;
  created_at: string;
  answer_details: AnswerDetail[];
  form?: {
    questions: any[];
  };
  is_complete?: boolean;
}

interface AllFormsProps {
  data: {
    id: string;
    first_name: string;
    last_name: string;
    form_title?: string;
    form_answers?: FormAnswer[];
  };
}

const AllForms = ({ data }: AllFormsProps) => {
  const [selectedForm, setSelectedForm] = useState<FormAnswer | null>(null);

  if (!data || !data?.form_answers || data?.form_answers.length === 0) {
    return <EmptyState text="No form answers found" />;
  }

  if (selectedForm) {
    return (
      <Answers answer={selectedForm} onBack={() => setSelectedForm(null)} />
    );
  }

  return (
    <Box width="100%">
      {data.form_answers
        .sort(
          (a: any, b: any) =>
            moment(b.created_at).valueOf() - moment(a.created_at).valueOf()
        )
        .map((formAnswer) => {
          const answer_details = formAnswer.answer_details || [];

          // Use your specific completion logic
          const adjustedTotalQuestions = Math.max(
            formAnswer.form?.questions?.length || 0
          );
          const isComplete = answer_details.length >= adjustedTotalQuestions;

          const formattedDate = moment(formAnswer.created_at).format(
            'MMM D, YYYY h:mma'
          );

          return (
            <Box
              key={formAnswer.id}
              borderRadius="md"
              width={'100%'}
              p={4}
              bg="white"
              cursor="pointer"
              _hover={{ backgroundColor: Colors.ORANGE.LIGHT }}
              boxShadow="md"
              transition="all 0.2s ease-in-out"
              mb={4}
              borderLeft={`4px solid ${isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY}`}
              onClick={() => setSelectedForm(formAnswer)}
            >
              <Flex justify="space-between" align="center">
                {/* Left Side */}
                <Flex align="center" gap={3}>
                  <Center
                    h={'40px'}
                    w={'40px'}
                    bg={
                      isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY
                    }
                    rounded={'full'}
                  >
                    {isComplete ? (
                      <FaCheck size={15} color="white" />
                    ) : (
                      <FaExclamation size={15} color="white" />
                    )}
                  </Center>
                  <Flex direction="column">
                    <Text fontWeight="500">
                      {formAnswer?.form_title || 'Untitled Form'}
                    </Text>
                    <Text fontSize="sm" color="#7C7C7C">
                      Submitted: {formattedDate}
                      {!isComplete && (
                        <Text as="span" color={Colors.ORANGE.PRIMARY} ml={2}>
                          (Incomplete)
                        </Text>
                      )}
                    </Text>
                  </Flex>
                </Flex>

                {/* Right Side */}
                <Box textAlign="right" fontSize="12px">
                  <Text fontWeight="500">
                    {adjustedTotalQuestions} question
                    {adjustedTotalQuestions !== 1 ? 's' : ''}
                  </Text>
                  <Text
                    color={
                      isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY
                    }
                  >
                    {answer_details.length}/{adjustedTotalQuestions} answered
                  </Text>
                </Box>
              </Flex>
            </Box>
          );
        })}
    </Box>
  );
};

export default AllForms;
