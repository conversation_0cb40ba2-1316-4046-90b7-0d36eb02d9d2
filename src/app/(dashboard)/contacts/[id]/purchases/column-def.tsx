import { Box, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import moment from 'moment';
import RowAction from './RowAction';
import Status from '@/components/elements/status/Status';

const columnHelper = createColumnHelper<any>();

export const createColumnDef = () => {
  return [
    columnHelper.display({
      cell: (props) => (
        <Box minW={'5rem'}>{props.row.original?.service?.name}</Box>
      ),
      header: 'Service',
      id: 'invoice-no',
    }),

    columnHelper.accessor('created_at', {
      cell: (info) => {
        return (
          <Box minW={'5rem'}>
            {info.getValue() ? (
              <p>{moment(info.getValue()).utc().format('MMMM D, YYYY')}</p>
            ) : (
              <Text color={'red'}>MISSING</Text>
            )}
          </Box>
        );
      },
      header: 'Date',
      id: 'date',
    }),
    columnHelper.display({
      cell: (props) => (
        <Status name={props.row.original?.status?.toLowerCase()} />
      ),
      header: 'Status',
      id: 'status',
    }),
    columnHelper.display({
      cell: (props) => <RowAction purchase={props.row.original} />,
      header: 'Action',
      id: 'action',
    }),
  ];
};
