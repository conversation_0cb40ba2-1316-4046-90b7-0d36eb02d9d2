import { useUpdatePurchaseMutation } from '@/api/newsf/queries';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { BsThreeDotsVertical } from 'react-icons/bs';

export default function RowAction({ purchase }: { purchase: any }) {
  const queryClient = useQueryClient();
  const statusDisclosure = useDisclosure();
  const { mutateAsync: UpdatePurchase, isLoading: UpdatePurchasePending } =
    useUpdatePurchaseMutation();
  const isLinked = purchase?.status?.toLowerCase() === 'linked';

  const handleStatusUpdate = async () => {
    await UpdatePurchase({
      id: purchase.id,
      payload: {
        status: isLinked ? 'UNLINKED' : 'LINKED',
      },
    });
    toaster.create({
      description: ToastMessages.operationSuccess,
      type: 'success',
    });
    statusDisclosure.onClose();
    await queryClient.invalidateQueries({
      queryKey: [queryKey.newSf.getAllPurchases],
    });
  };

  return (
    <div data-no-row-click="true">
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger
          border={'none !important'}
          boxShadow={'none !important'}
          cursor={'pointer'}
        >
          <BsThreeDotsVertical />
        </MenuTrigger>
        {purchase?.status !== 'DELETED' && (
          <MenuContent cursor={'pointer'}>
            <MenuItem value="update-status" onClick={statusDisclosure.onOpen}>
              {isLinked ? 'Unlink' : 'Link'}
            </MenuItem>
            {/* <MenuSeparator /> */}
          </MenuContent>
        )}
      </MenuRoot>

      <ConsentDialog
        handleSubmit={handleStatusUpdate}
        open={statusDisclosure.open}
        onOpenChange={statusDisclosure.onClose}
        heading={isLinked ? 'Unlink Purchase' : 'Link Purchase?'}
        note={`This will mark this purchase as ${isLinked ? 'Unlinked' : 'Linked'}`}
        isLoading={UpdatePurchasePending}
      />
    </div>
  );
}
