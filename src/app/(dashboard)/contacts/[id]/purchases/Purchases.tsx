'use client';

import { useGetAllPurchases } from '@/api/newsf/queries';
import SectionLoader from '@/components/elements/loader/SectionLoader';
import CustomTable from '@/components/table/CustomTable';
import { Box, Flex, Heading } from '@chakra-ui/react';
import { createColumnDef } from './column-def';
import { useState } from 'react';
import EmptyState from '@/components/elements/EmptyState';

export default function Purchases({ id }: { id: string }) {
  const [filter, setFilter] = useState<any>({ client_id: id });
  const { data: PurchasesData, isLoading: PurchasesLoading } =
    useGetAllPurchases(filter);

  // console.log('PurchasesData is ', PurchasesData);

  return (
    <Box spaceY={'4'}>
      <Flex
        flexDirection={'column'}
        alignItems={'start'}
        gap={'6px'}
        width={{ base: 'full', md: '25rem' }}
        mb={{ base: '2', md: '4' }}
      >
        <Heading fontSize={{ base: '1.3rem', md: '1.5rem' }}>
          Purchases({PurchasesData?.pagination?.total_count || 0})
        </Heading>
      </Flex>

      {PurchasesLoading ? (
        <SectionLoader />
      ) : PurchasesData?.data && PurchasesData?.data?.length > 0 ? (
        <CustomTable
          columnDef={createColumnDef()}
          data={PurchasesData?.data || []}
          total={PurchasesData?.pagination?.total_count}
          pagination={{
            row: Number(filter?.size),
            page: Number(filter?.currentPage),
          }}
          setPagination={{
            onPageChange: (e) => setFilter({ ...filter, page_number: e }),
            onRowChange: (e) => setFilter({ ...filter, items_per_page: e }),
          }}
          // onRowClick={onRowClick}
          // tableFooter={isRaw && <TableFooter data={data} />}
        />
      ) : (
        <EmptyState text="No purchases" />
      )}
    </Box>
  );
}
