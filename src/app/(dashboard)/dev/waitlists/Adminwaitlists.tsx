'use client';

import { Box, Center, Heading } from '@chakra-ui/react';
import { useGetAllWaitlists } from '@/api/wishlist';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { WaitlistcolumnDef } from './WaitlistColumnDef';

const Adminwaitlists = () => {
  const { data, isLoading } = useGetAllWaitlists();
  return (
    <div style={{ paddingTop: '30px', paddingBottom: '30px' }}>
      <Heading fontWeight={500} fontSize={'2.5rem'}>
        Waitlists
      </Heading>
      <Box minH={'20rem'} mt={'8'}>
        {isLoading ? (
          <Center h={'20rem'}>
            <AnimateLoader />
          </Center>
        ) : (
          <CustomTable
            columnDef={WaitlistcolumnDef}
            data={data}
            total={data?.length || 0} // Reflect the count of filtered data
            // tableOptions={{
            //   pageCount: 1,
            //   manualPagination: true,
            //   getCoreRowModel: getCoreRowModel(),
            //   getSortedRowModel: getSortedRowModel(),
            //   enableMultiSort: true,
            //   enableRowSelection: true,
            // }}
            // pagination={{
            //   row: Number(filter?.size || 0),
            //   page: Number(filter?.currentPage || 1),
            // }}
            // setPagination={{
            //   onPageChange: (e) => setFilter({ ...filter, currentPage: e }),
            //   onRowChange: (e) => setFilter({ ...filter, size: e }),
            // }}
          />
        )}
      </Box>
    </div>
  );
};

export default Adminwaitlists;
