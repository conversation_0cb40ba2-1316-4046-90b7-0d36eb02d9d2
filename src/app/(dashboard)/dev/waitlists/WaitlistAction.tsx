'use client';

import {
  <PERSON>u<PERSON>ontent,
  <PERSON>u<PERSON><PERSON>,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import supabase from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';

const WaitlistAction = ({
  id,
  isNotified,
}: {
  id: number;
  isNotified: boolean;
}) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);

  const handleSendNotification = async () => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from('waitlists')
        .update({ notified: true })
        .eq('id', id);
      if (error) {
        console.error('Failed to update waitlist:', error.message);
        toaster.create({
          type: 'error',
          description: 'Failed to send notification.',
        });
        throw new Error('Failed to update waitlist');
      }
      queryClient.invalidateQueries({
        queryKey: [queryKey.overview.getAllWaitlists],
      });
      toaster.create({
        type: 'success',
        description: 'Notification sent successfully.',
      });
    } catch (error) {
      console.error('Error sending notification:', error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent cursor={'pointer'}>
          <MenuItem
            value="notify"
            disabled={loading}
            onClick={handleSendNotification}
          >
            {isNotified ? 'Resend Notification' : 'Send Notification'}
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </div>
  );
};

export default WaitlistAction;
