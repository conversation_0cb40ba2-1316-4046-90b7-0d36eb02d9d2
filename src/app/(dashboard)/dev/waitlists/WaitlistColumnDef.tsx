import { Box } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import { IWaitlist } from '@/shared/interface/wishlists';
import Status from '@/components/elements/status/Status';
import WaitlistAction from './WaitlistAction';

const columnHelper = createColumnHelper<IWaitlist>();

export const WaitlistcolumnDef = [
  columnHelper.accessor('name', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Name',
    id: 'name',
  }),
  columnHelper.accessor('email', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Email',
    id: 'email',
  }),
  columnHelper.accessor('user_type', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Profession',
    id: 'user_type',
  }),
  columnHelper.accessor('notified', {
    cell: (info) => (
      <Box>
        <Status name={info.getValue() ? 'Yes' : 'No'} />
      </Box>
    ),
    header: 'Notified',
    id: 'notified',
  }),
  columnHelper.display({
    id: 'action',
    cell: (info) => {
      const rowId = info.row.original.id;
      return (
        <WaitlistAction
          id={rowId}
          isNotified={info.row.original.notified ? true : false}
        />
      );
    },
  }),
];
