import {
  useGetAllTrackableEventQuery,
  useGetKlaviyoOrganization,
  useUpdateKlaviyoOrganization,
  useUpdateTrackableEventMutation,
} from '@/api/klaviyo/queries';
import SectionLoader from '@/components/elements/loader/SectionLoader';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import {
  // DialogActionTrigger,
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from '@/components/ui/dialog';
import { KlaviyoActions } from '@/constants/klaviyo-actions';
import supabase from '@/lib/supabase/client';
import {
  Box,
  Checkbox,
  CheckboxGroup,
  Fieldset,
  For,
  // Stack,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

export default function KlaviyoEdit({ user, open, setOpen }: any) {
  const [newItems, setNewItems] = useState([]);
  const [listId, setListId] = useState('');
  const {
    data: TrackableEvent,
    isLoading: TrackableEventLoading,
    refetch,
  } = useGetAllTrackableEventQuery(user?.organization_id, {
    enabled: Boolean(user?.organization_id),
  });
  const { data: KOrg, isLoading: KOrgLoading } = useGetKlaviyoOrganization(
    user?.organization_id,
    supabase,
    {
      enabled: Boolean(user?.organization_id),
    }
  );

  const { mutateAsync, isLoading: UpdateLoading } =
    useUpdateTrackableEventMutation();
  const { mutateAsync: UpdateKlaviyoOrg, isLoading: UpdateKlaviyoOrgLoading } =
    useUpdateKlaviyoOrganization();
  // console.log('newItems is ', newItems);
  // console.log('TrackableEvent is ', TrackableEvent);
  const handleSubmit = async () => {
    await mutateAsync({
      organization_id: user?.organization_id,
      payload: newItems,
    });
    if (KOrg.list_id !== listId) {
      UpdateKlaviyoOrg({
        supabase,
        payload: { list_id: listId },
        organization_id: user?.organization_id,
      });
    }
    await refetch();
    setOpen(false);
  };

  useEffect(() => {
    if (KOrg && !listId) {
      setListId(KOrg.list_id);
    }
  }, [KOrg, listId]);
  useEffect(() => {
    if (TrackableEvent?.data?.length > 0) {
      setNewItems(TrackableEvent?.data?.map((item: any) => item?.event_name));
    }
  }, [TrackableEvent]);
  return (
    <div>
      <DialogRoot open={open} size={'sm'} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>Edit Events to Dispatch</DialogHeader>
          <DialogBody>
            {TrackableEventLoading || KOrgLoading ? (
              <SectionLoader />
            ) : (
              <Fieldset.Root>
                <CheckboxGroup
                  onValueChange={(e: any) => setNewItems(e)}
                  name="framework"
                  value={newItems}
                >
                  <Fieldset.Legend fontSize="sm" mb="2">
                    Select Actions To Track
                  </Fieldset.Legend>
                  <Fieldset.Content>
                    <For each={Object.values(KlaviyoActions)}>
                      {(value) => (
                        <Checkbox.Root key={value} value={value}>
                          <Checkbox.HiddenInput />
                          <Checkbox.Control />
                          <Checkbox.Label>{value}</Checkbox.Label>
                        </Checkbox.Root>
                      )}
                    </For>
                  </Fieldset.Content>
                </CheckboxGroup>
              </Fieldset.Root>
            )}

            <Box mt={'1rem'}>
              <StringInput
                inputProps={{
                  name: 'listId',
                  placeholder: 'V72rdt',
                  value: listId,
                  onChange: (e: any) => setListId(e.target.value),
                }}
                fieldProps={{ label: 'Enter List id' }}
              />
            </Box>
          </DialogBody>
          <DialogFooter flexDir={'column'} mb={'8'} gap={'1rem'}>
            {/* <Button
              w={'full'}
              bg={'primary.500'}
              //   onClick={handleSubmit}
              //   loading={isLoading}
              data-no-row-click="true"
            >
              Cancel{' '}
            </Button> */}
            {/* <DialogActionTrigger asChild> */}
            <Button
              onClick={handleSubmit}
              w={'full'}
              loading={UpdateLoading || UpdateKlaviyoOrgLoading}
            >
              Proceed
            </Button>
            {/* </DialogActionTrigger> */}
          </DialogFooter>
          <DialogCloseTrigger />
        </DialogContent>
      </DialogRoot>
    </div>
  );
}
