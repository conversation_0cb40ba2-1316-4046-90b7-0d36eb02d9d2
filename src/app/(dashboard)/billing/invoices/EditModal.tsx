import SearchContact from '@/components/elements/search/SearchContact';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { useEditInvoiceHook } from '@/hooks/billing/invoice/useEditInvoiceHook';
import {
  Box,
  chakra,
  Flex,
  Grid,
  GridItem,
  Heading,
  HStack,
  Separator,
  Stack,
  Text,
} from '@chakra-ui/react';
import { getLinkedClientOptionsFromArray } from '@/utils/get-options-from-array';
import { useGetLinkedClientsQuery } from '@/api/linked_clients/get-linked-clients';
import { BsX } from 'react-icons/bs';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import moment from 'moment';

export function EditModal({ invoice, onClose, isOpen, clientId }: any) {
  const {
    handleFormSubmit,
    values,
    handleChange,
    handleBlur,
    setFieldValue,
    touched,
    statusOptions,
    sessionTypeOptions,
    errors,
    slpOptions,
    updateLoading,
    sessionDuration,
    searchResult,
    setSearchResult,
    selectExistingUser,
    linked,
    setLinked,
    slpInfo,
    user,
    setUser,
  } = useEditInvoiceHook({ invoice: invoice as any, onClose });

  const { data: LinkedClients, isLoading: LinkedClientLoading } =
    useGetLinkedClientsQuery(invoice?.client_id as any, {
      enabled: Boolean(invoice?.client_id) && isOpen,
    });

  const linkedClientOptions: any = getLinkedClientOptionsFromArray(
    LinkedClients || []
  );

  return (
    <CustomModal w={'30rem'} onOpenChange={onClose} open={isOpen}>
      <Box>
        <Text textAlign={'center'} fontWeight={'semibold'} mb={'2rem'}>
          Edit Invoice {invoice.invoice_number}
        </Text>
        <chakra.form onSubmit={handleFormSubmit}>
          <Stack alignItems={'center'}>
            <Box width={'100%'}>
              <label className="font-medium text-gray-900">Lookup Client</label>
              <SearchContact
                // value={
                //   user ? `${user?.first_name} ${user?.last_name}` : invoice.name
                // }
                setSearchResult={(e: any) => {
                  setSearchResult(e);
                }}
                selectExistingUser={selectExistingUser}
                searchResult={searchResult}
              />
            </Box>

            {!LinkedClientLoading && linkedClientOptions?.length > 1 && (
              <Box w={'full'}>
                <CustomSelect
                  placeholder="Select one"
                  options={linkedClientOptions}
                  onChange={(val) => {
                    // Set complete user object with all necessary data
                    if (val.client) {
                      setUser({
                        id: val.value, // This is the client_id
                        first_name: val.client.first_name || '',
                        last_name: val.client.last_name || '',
                        email: val.client.emails || null,
                      });

                      // Update form values if needed
                      setFieldValue('client_id', val.value);
                      setFieldValue(
                        'name',
                        `${val.client.first_name || ''} ${val.client.last_name || ''}`.trim()
                      );
                      if (val.client.emails) {
                        setFieldValue('email', val.client.emails);
                      }
                    }
                  }}
                  label="Linked Clients"
                  defaultValue={linkedClientOptions?.find(
                    (item: any) => Number(item?.value) === Number(clientId)
                  )}
                  // Use the current user.id to find the selected option
                  value={linkedClientOptions?.find(
                    (item: any) => Number(item?.value) === Number(user?.id)
                  )}
                />
              </Box>
            )}

            <StringInput
              inputProps={{
                name: 'Name',
                value: user
                  ? `${user?.first_name} ${user?.last_name}`
                  : invoice.name,
                readOnly: true,
              }}
              fieldProps={{
                label: 'Client Name',
              }}
            />
            <StringInput
              inputProps={{
                name: 'product',
                onChange: handleChange,
                value: values.product as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Product Description',
              }}
            />

            <StringInput
              inputProps={{
                name: 'invoice_date',
                onChange: handleChange,
                value: values.invoice_date?.split('T')[0],
                onBlur: handleBlur,
                type: 'date',
              }}
              fieldProps={{
                label: 'Invoice Date',
              }}
            />

            <Grid templateColumns="repeat(2, 1fr)" gap={6} width="100%">
              <GridItem>
                <StringInput
                  fieldProps={{
                    label: 'Qty',
                    invalid: touched.qty && !!errors.qty,
                    required: true,
                    errorText: errors?.qty as any,
                  }}
                  inputProps={{
                    name: 'qty',
                    onChange: handleChange,
                    value: values.qty as any,
                    onBlur: handleBlur,
                  }}
                  // error={!!errors.qty}
                  // errorMessage={errors?.qty}
                  // touched={touched.qty}
                />
              </GridItem>
              <GridItem>
                <Box w={'100%'}>
                  <CustomSelect
                    placeholder="Session Type"
                    options={sessionTypeOptions || []}
                    onChange={(val) => {
                      setFieldValue('session_type', val.value);
                    }}
                    label="Session Type"
                    defaultValue={
                      sessionTypeOptions &&
                      sessionTypeOptions?.find(
                        (item: any) => item?.value === values.session_type
                      )
                    }
                  />
                </Box>
              </GridItem>
            </Grid>

            <Grid templateColumns="repeat(2, 1fr)" gap={6} width="100%">
              <GridItem>
                <CustomSelect
                  placeholder="Total Minutes"
                  onChange={(val) => setFieldValue('total_hours', val?.value)}
                  options={sessionDuration}
                  required={true}
                  defaultValue={sessionDuration?.find(
                    (item: any) => Number(item.value) === values?.total_hours
                  )}
                  label="Total Minutes"
                />
              </GridItem>
              <GridItem>
                <StringInput
                  inputProps={{
                    type: 'number',
                    name: 'total_price',
                    onChange: handleChange,
                    value: values.total_price as any,
                    onBlur: handleBlur,
                  }}
                  fieldProps={{
                    label: 'Total Price',
                    invalid: touched.total_price && !!errors.total_price,
                    required: true,
                    errorText: errors?.total_price as any,
                  }}
                />
              </GridItem>
            </Grid>

            <StringInput
              inputProps={{
                name: 'memo',
                onChange: handleChange,
                value: values.memo as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Memo',
              }}
            />
            <StringInput
              inputProps={{
                name: 'invoice_number',
                onChange: handleChange,
                value: values.invoice_number as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Invoice Number',
              }}
            />

            <Box w={'100%'}>
              <CustomSelect
                placeholder="Slp"
                options={slpOptions || []}
                onChange={(val) => {
                  setFieldValue('slp_id', val.value);
                }}
                label="SLP"
                defaultValue={
                  slpOptions &&
                  slpOptions?.find((item: any) => item?.value === values.slp_id)
                }
              />
            </Box>

            <Box w={'100%'}>
              <CustomSelect
                placeholder="Unchanged"
                options={statusOptions}
                onChange={(val) => {
                  setFieldValue('status', val.value);
                }}
                label="Status"
                defaultValue={statusOptions.find(
                  (item: any) =>
                    item.value.toLowerCase() === values?.status.toLowerCase()
                )}
              />
            </Box>

            <CustomTextArea
              inputProps={{
                name: 'change_reason',
                onChange: handleChange,
                value: values.change_reason as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Change Reason',
                invalid: touched.change_reason && !!errors.change_reason,
                errorText: errors?.change_reason as any,
              }}
              // errorMessage={errors?.change_reason}
              // touched={touched.change_reason}
            />
          </Stack>
          <Separator mt={'1rem'} />

          {invoice.slp_notes?.length > 0 && linked ? (
            <Box mt={'.5rem'} w={'100%'}>
              <HStack justifyContent={'space-between'}>
                <Heading fontSize={'22px'}>SLP Note Info</Heading>
                <Box onClick={() => setLinked(!linked)}>
                  <BsX size={24} color="red" />
                </Box>
              </HStack>
              <HStack
                justifyContent={'space-between'}
                alignItems={'flex-start'}
              >
                <Stack width={'100%'} gap={2}>
                  <HStack mt={'.4rem'}>
                    <Text fontWeight={600}>Client Name:</Text>
                    <Text>{`${invoice?.name}`}</Text>
                  </HStack>
                  <HStack alignItems={'flex-start'}>
                    <Text fontWeight={600}>Product:</Text>
                    <Text> {invoice?.product}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>Memo:</Text>
                    <Text> {invoice?.slp_notes?.[0]?.invoice_memo}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>SLP:</Text>
                    <Text> {slpInfo?.email}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>Session Date:</Text>
                    <Text>
                      {' '}
                      {moment(invoice?.invoice_date)
                        .utc()
                        .format('MMMM D, YYYY')}
                    </Text>
                  </HStack>
                </Stack>
              </HStack>
            </Box>
          ) : (
            <Box w={'100%'}>
              <Text color={'red'} fontWeight={500}>
                No SLP notes found
              </Text>
            </Box>
          )}
          <Flex
            my={'2rem'}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Button onClick={onClose} bg={'gray'} _hover={{ bg: 'gray' }}>
              Cancel
            </Button>
            <Button bg={'primary.500'} loading={updateLoading} type="submit">
              Update
            </Button>
          </Flex>
        </chakra.form>
      </Box>
    </CustomModal>
  );
}
