import { useState, useMemo } from 'react';
import { generateAndDownloadPDF } from '@/components/elements/pdf/Generate-PDF';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import { Box, useDisclosure } from '@chakra-ui/react';
import moment from 'moment';
import 'moment-timezone';
import 'moment/locale/en-ca';
import Link from 'next/link';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { CreateAndSendPDF } from './CreateAndSendEmail';
import DeleteInvoice from './DeleteInvoice';
import LinkTransactions from './LinkTransactions';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { copyToClipboard } from '@/utils/copy-value';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { EditModal } from './EditModal';
import { Spinner } from '@chakra-ui/react';

interface EditInvoiceProps {
  invoice: any;
  color?: string;
  clientId?: string;
}

export default function EditInvoice({
  invoice,
  color,
  clientId,
}: EditInvoiceProps) {
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const { open, onClose, onOpen } = useDisclosure();
  const CSEmailDisclosure = useDisclosure();
  const deleteDisclosure = useDisclosure();
  const { UserFromQuery } = useSupabaseSession();

  const { transactions, total_price, currency_code } = invoice ?? {};

  const amountDue = useMemo(() => {
    const totalPaid = transactions?.reduce(
      (sum: number, transaction: any) => sum + Number(transaction?.amount || 0),
      0
    );
    return Number(total_price) - Number(totalPaid);
  }, [total_price, transactions]);

  const resolvedAmountDue = useMemo(() => {
    const formatted = formatMoney(Math.abs(amountDue), {
      currencyCode: currency_code,
    });
    return amountDue < 0 ? `(${formatted})` : formatted;
  }, [amountDue, currency_code]);

  const handleDownload = async () => {
    try {
      await generateAndDownloadPDF({
        name: invoice?.clients?.display_name || invoice?.name,
        receiptNumber: invoice?.invoice_number,
        date: moment(invoice?.invoice_date).format('YYYY-MM-DD'),
        activity: invoice?.services?.name || '',
        quantity: Number(invoice?.qty),
        rate: Number(invoice?.total_price),
        balance: 0,
        memo: invoice?.memo || '',
        referral: invoice?.referral,
        amountDue,
        dueDate: invoice?.due_date,
        email: invoice?.email,
        transactions,
        resolvedAmountDue,
        invoice,
      });
      toaster.create({
        type: 'success',
        description: 'PDF successfully downloaded',
      });
    } catch (error) {
      toaster.create({
        type: 'error',
        description: `Failed to download PDF: ${error}`,
      });
    }
  };

  const handlePaymentLink = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent menu from closing
    setIsGeneratingLink(true);
    try {
      const response = await fetch('/api/stripe/invoice-payment-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          invoiceId: invoice.id,
          user_id: invoice.slp_id,
        }),
      });

      if (!response.ok) {
        throw new Error(
          (await response.json())?.message || 'Failed to generate link'
        );
      }

      const { link } = await response.json();

      const activeElement = document.activeElement as HTMLElement;
      activeElement?.blur(); // Remove focus from current element
      document.body.focus(); // Set focus to body
      await copyToClipboard(link);
      toaster.create({
        description: 'Payment link copied to clipboard',
        type: 'success',
      });
    } catch (error: any) {
      toaster.create({
        description: error.message || 'Failed to generate payment link',
        type: 'error',
      });
    } finally {
      setIsGeneratingLink(false);
    }
  };

  const isOrg1 = invoice?.organization_id === 1;

  return (
    <div data-no-row-click="true">
      <MenuRoot positioning={{ placement: 'bottom' }}>
        <MenuTrigger asChild>
          <Box cursor={'pointer'} className="cursor-pointer">
            {isGeneratingLink ? <Spinner size="sm" /> : <BsThreeDotsVertical />}
          </Box>
        </MenuTrigger>
        <MenuContent>
          {isOrg1 ? (
            <MenuItem value="edit" onClick={onOpen} color={color}>
              Edit
            </MenuItem>
          ) : (
            <>
              <MenuItem value="view" asChild>
                <Link href={`/invoices/${invoice?.id}`}>View</Link>
              </MenuItem>
              {UserFromQuery?.organization?.stripe_user_id && (
                <MenuItem
                  onClick={handlePaymentLink}
                  disabled={isGeneratingLink}
                  value="g-pay-link"
                >
                  Generate Payment Link
                </MenuItem>
              )}
            </>
          )}

          {isOrg1 && (
            <>
              <MenuSeparator />
              <MenuItem value="generate-pdf" onClick={handleDownload}>
                Generate PDF
              </MenuItem>
              <MenuSeparator />
              <MenuItem value="send-email" onClick={CSEmailDisclosure.onOpen}>
                Create and Send Email
              </MenuItem>
              <MenuSeparator />
              <MenuItem value="link-transaction">
                <LinkTransactions invoice={invoice} />
              </MenuItem>
              <MenuSeparator />
              <MenuItem
                value="delete"
                onClick={deleteDisclosure.onOpen}
                color="red"
              >
                Delete
              </MenuItem>
            </>
          )}
        </MenuContent>
      </MenuRoot>

      {open && (
        <EditModal
          isOpen={open}
          onClose={onClose}
          invoice={invoice}
          clientId={clientId}
        />
      )}
      {CSEmailDisclosure.open && (
        <CreateAndSendPDF
          isOpen={CSEmailDisclosure.open}
          onCloseModal={CSEmailDisclosure.onClose}
          invoice={invoice}
          isInvoiceLoading={false}
        />
      )}
      {deleteDisclosure.open && (
        <DeleteInvoice
          open={deleteDisclosure.open}
          onClose={deleteDisclosure.onClose}
          row={invoice}
        />
      )}
    </div>
  );
}
