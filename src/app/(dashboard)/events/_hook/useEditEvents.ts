/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */

import { useGetAllFormsQuery } from '@/api/forms/get-all-forms';
import { useCreateServiceMutation } from '@/api/services/create-service';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { eventSchema } from '@/form-handling/validations/events';
import { useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FormEvent, useEffect, useState } from 'react';

export const useEditEvent = (eventDetails: any, userFromServer: any) => {
  console.log('eventDetails', eventDetails);
  const { onClose, onOpen, open } = useDisclosure();
  const [loading, setLoading] = useState(false);
  const [isExisting, setIsExisting] = useState(true);

  const router = useRouter();
  const [allSelectedQuestions, setAllSelectedQuestions] = useState<any>(
    eventDetails?.form_config
  );

  const { data: GetAllFormsData, isLoading: GetAllFormsLoading } =
    useGetAllFormsQuery({
      organizationId: userFromServer?.organization_id,
    });

  console.log('GetAllFormsData', GetAllFormsData);
  const [linkedFormUrl, setLinkedFormUrl] = useState('');

  //console.log('allSelectedQuestions', allSelectedQuestions);

  const [selectedQuestionType, setSelectedQuestionType] = useState<any>(null);

  const { data: AllServices } = useGetServicesQuery(
    userFromServer?.organization_id,
    {
      enabled: Boolean(userFromServer?.organization_id),
    }
  );
  const { mutateAsync: CreateServiceApi, isLoading: CreateServiceLoading } =
    useCreateServiceMutation();

  const linkServiceDisclosure = useDisclosure();

  const generateLinkedFormUrl = (form: any) => {
    const organization_slug = `${window?.location?.origin}/form/${userFromServer?.organization?.slug}/${form?.slug}`;

    console.log('organization_slug', organization_slug);
    setLinkedFormUrl(organization_slug);
  };

  // Initialize linkedForm from eventDetails on component mount
  useEffect(() => {
    if (eventDetails?.linked_form_url && GetAllFormsData?.allForms) {
      // Try to find the form that matches the linked_form_url
      const matchedForm = GetAllFormsData.allForms.find((form: any) => {
        const formUrl = `${window?.location?.origin}/form/${userFromServer?.organization?.slug}/${form?.slug}`;
        return formUrl === eventDetails.linked_form_url;
      });

      if (matchedForm) {
        setFieldValue('linkedForm', matchedForm);
        setLinkedFormUrl(eventDetails.linked_form_url);
      }
    }
  }, [eventDetails, GetAllFormsData, userFromServer]);

  const {
    values,
    handleChange,
    errors,
    touched,
    handleSubmit,
    setFieldValue,
    setErrors,
  } = useFormik<any>({
    validationSchema: eventSchema,

    initialValues: {
      ...eventDetails,
      linkedForm: null, // Initialize as null, will be set by useEffect above
    },
    onSubmit: async () => {
      //   console.log('values is ', { ...values });
      try {
        setLoading(true);
        let service_id = values?.service?.id || null;

        const {
          availabilities,
          availability,
          created_at,
          service,
          linkedForm,
          ...editedEvent
        } = values;
        const newService = service && !service?.id;

        //console.log('new service  is ', newService);
        // return;

        const editedAvailability = availabilities.map((item: any) => ({
          available: item.available,
          id: item.id,
          day: item.day,
          start_time: item.startTime,
          end_time: item.endTime,
        }));

        if (newService) {
          //console.log('trying to create new service ');
          // return;

          const response = await CreateServiceApi({
            ...service,
            duration_minutes: values?.duration,
            organization_id: userFromServer?.organization_id,
          });
          service_id = response?.data?.id;
        }

        // Generate the linked_form_url from the selected form
        let linked_form_url = null;
        if (values?.linkedForm) {
          linked_form_url = `${window?.location?.origin}/form/${userFromServer?.organization?.slug}/${values.linkedForm.slug}`;
        }

        // console.log('editedAvailability is', editedAvailability);
        console.log('payload is ', {
          ...editedEvent,
          service_id,
          updated_at: new Date(),
          form_config: allSelectedQuestions,
          linked_form_url, // Include the linked form URL in the payload
        });

        // return;

        const editEventRes = await fetch(`/api/events/${eventDetails.id}`, {
          method: 'PATCH',
          body: JSON.stringify({
            payload: {
              ...editedEvent,
              service_id: values?.service?.id || null,
              updated_at: new Date(),
              form_config: allSelectedQuestions,
              linked_form_url: linked_form_url ? linked_form_url : null,
            },
          }),
          cache: 'no-store',
        });

        if (!editEventRes.ok) {
          throw new Error(await editEventRes.json());
        }

        const editAvailabilityRes = await fetch(`/api/availability`, {
          method: 'PATCH',
          body: JSON.stringify({
            payload: editedAvailability,
          }),
          cache: 'no-store',
        });

        if (!editAvailabilityRes.ok) {
          throw new Error(await editAvailabilityRes.json());
        }

        toaster.create({
          type: 'success',
          description: 'Event updated successfully!',
        });

        router.push('/events');
        router.refresh();
      } catch (error: any) {
        toaster.create({
          type: 'error',
          description: error?.message,
        });
      } finally {
        setLoading(false);
      }
    },
  });

  const handleCreateNewService = (e: FormEvent) => {
    e.preventDefault();
    const service = values?.service;
    //console.log('service is ', service);

    if (!service?.price || !service?.name || !values?.duration) {
      toaster.create({
        description: 'Invalid inputs',
        type: 'error',
      });
      return;
    }
    linkServiceDisclosure.onClose();
  };

  useEffect(() => {
    if (eventDetails?.service_id && AllServices?.services?.length > 0) {
      const selectedService = AllServices?.services?.find(
        (item: any) => item?.id === eventDetails?.service_id
      );

      setFieldValue('service', selectedService);
    }
  }, [eventDetails, AllServices, setFieldValue]);

  const generateUniqueId = () => {
    return `q-${moment().valueOf()}-${Math.floor(Math.random() * 900) + 100}`;
    // Example: "q-1712345678901-427"
  };

  // console.log(
  //   'allSelectedQuestions from create event is',
  //   allSelectedQuestions
  // );
  //   console.log('formik error is ', createEventFormik.errors);
  // console.log('values is ', createEventFormik.values);

  const handleQuestionClick = (q: any) => {
    setSelectedQuestionType(q);
    onOpen();
  };

  const removeSelectedQuestion = (id: any) => {
    setAllSelectedQuestions((prev: any) =>
      prev.filter((q: any) => q.id !== id)
    );
  };

  const addQuestionToCurrentPage = (questionData: any) => {
    // console.log('questionData', questionData);
    const questionWithPage = {
      ...questionData,
      page: 1,
    };

    setAllSelectedQuestions((prev: any) => {
      const isEdit = prev.some((q: any) => q.id === questionData.id);
      if (isEdit) {
        return prev.map((q: any) =>
          q.id === questionData.id ? questionWithPage : q
        );
      } else {
        return [...prev, questionWithPage];
      }
    });
  };

  return {
    loading,
    handleChange,
    errors,
    touched,
    handleSubmit,
    addQuestionToCurrentPage,
    setFieldValue,
    generateLinkedFormUrl,
    linkedFormUrl,
    setErrors,
    values,
    selectedQuestionType,
    handleQuestionClick,
    removeSelectedQuestion,
    onOpen,
    onClose,
    allSelectedQuestions,
    generateUniqueId,
    GetAllFormsLoading,
    setAllSelectedQuestions,
    open,
    linkServiceDisclosure,
    handleCreateNewService,
    setIsExisting,
    isExisting,
    GetAllFormsData,
    CreateServiceLoading,
    AllServices,
    createEventFormik: {
      values,
      handleChange,
      errors,
      touched,
      handleSubmit,
      setFieldValue,
      setErrors,
      CreateServiceLoading,
    },
  };
};

export type TEditEventHook = ReturnType<typeof useEditEvent>;
