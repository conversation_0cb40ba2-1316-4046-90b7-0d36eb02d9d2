import { Box, Flex, Heading, Stack, Text } from '@chakra-ui/react';
import Image from 'next/image';
import React from 'react';
import sfLogo from '@/assets/logo.png';
import { AiOutlineExclamationCircle } from 'react-icons/ai';
import { FaRegClock } from 'react-icons/fa6';
import { CiWallet } from 'react-icons/ci';
import { formatNumber } from '@/utils/num-format';

export default function EventDetails({ eventData, organizationDetails }: any) {
  return (
    <Stack>
      <Box pt={'1.5rem'}>
        <Image
          alt={organizationDetails?.name}
          src={organizationDetails?.logo_url || sfLogo.src}
          width={150}
          height={150}
        />
      </Box>
      <Text
        mt={'.5rem'}
        color={'gray.400'}
        textTransform={'capitalize'}
        fontWeight={'semibold'}
      >
        {organizationDetails?.name}
      </Text>
      <Heading
        color={'gray.600'}
        textTransform={'capitalize'}
        fontWeight={'bold'}
      >
        {eventData.title}
      </Heading>
      <Flex alignItems={'center'} gap={'.5rem'} fontSize={'.75rem'}>
        <FaRegClock size={'1.3rem'} />
        <Text>{eventData?.duration} minutes</Text>
      </Flex>
      {eventData.product?.id && (
        <Flex alignItems={'flex-start'} gap={'.5rem'} mt={'.5rem'}>
          <CiWallet size={'1.3rem'} />
          <Text color={'gray.400'} fontSize={'.85rem'}>
            ${formatNumber(eventData.product?.price)}
          </Text>
        </Flex>
      )}
      <Flex alignItems={'flex-start'} gap={'.5rem'} mt={'.5rem'}>
        <AiOutlineExclamationCircle size={'1.3rem'} />
        <Text color={'gray.400'} fontSize={'.85rem'}>
          {eventData.description}
        </Text>
      </Flex>

      {eventData.product?.id && (
        <Stack gap={'1rem'} mt={'2rem'}>
          <Text>
            {' '}
            Your payment will be securely processed via our trusted payment
            provider. After completing your payment, you will be redirected back
            to our portal to fill out a required intake form before your
            assessment.
          </Text>

          <Text>
            You will also receive a payment receipt after your session, which
            you can submit to your insurance for potential reimbursement.
          </Text>
        </Stack>
      )}
    </Stack>
  );
}
