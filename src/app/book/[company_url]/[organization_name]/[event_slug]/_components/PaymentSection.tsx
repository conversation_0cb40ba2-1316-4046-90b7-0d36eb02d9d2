import { Box, Stack, Text } from '@chakra-ui/react';
import React from 'react';
import { CardElement } from '@stripe/react-stripe-js';
import StringInput from '@/components/Input/StringInput';
import { TUseBooking } from '../_hook/useBooking';

export default function PaymentSection({
  service,
  useBooking,
}: {
  service: any;
  useBooking: TUseBooking;
}) {
  const { setBookingForm, bookingForm } = useBooking;
  return (
    <Box w={'100%'}>
      <Text mb={'.4rem'}>Payment Information</Text>

      <Box border={'1px solid'} borderColor={'rgba(0,0,0,0.1)'} p={'1rem'}>
        <Text fontWeight={600}>Price</Text>
        <Text mb={'.5rem'}>${service?.price}</Text>
        <Text mb={'.5rem'} fontWeight={600}>
          Payment Terms
        </Text>
        <Text>
          Your credit card will be charged immediately upon booking. 24 hours
          notice is required to cancel or reschedule an appointment, otherwise
          the full fee for the missed appointment will be charged, except in
          cases of emergency or sudden illness. In the case of no shows, the
          full fee will be charged, no exceptions. Refunds will be subject to a
          5% processing fee.
        </Text>

        <Stack gap={'1rem'} my={'1rem'}>
          <StringInput
            fieldProps={{ required: true, label: 'Name On Card' }}
            inputProps={{
              value: bookingForm?.nameOnCard,
              onChange: (e) =>
                setBookingForm((prev: any) => ({
                  ...prev,
                  nameOnCard: e.target?.value,
                })),
            }}
          />

          <Box
            border="1px solid"
            borderColor="gray.300"
            borderRadius="md"
            p={3}
            _hover={{ borderColor: 'gray.400' }}
            _focusWithin={{
              borderColor: 'blue.500',
              boxShadow: '0 0 0 1px #4299e1',
            }} // Chakra's blue.500
            mt={'1rem'}
          >
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#32325d',
                    '::placeholder': {
                      color: '#a0aec0',
                    },
                  },
                  invalid: {
                    color: '#e53e3e',
                  },
                },
              }}
            />
          </Box>
        </Stack>
      </Box>
    </Box>
  );
}
