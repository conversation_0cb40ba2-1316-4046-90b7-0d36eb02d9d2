'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Box,
  Center,
  Flex,
  SimpleGrid,
  Spinner,
  Stack,
  Text,
} from '@chakra-ui/react';
// import { Box, Center, Flex, Spinner, Stack, Text } from '@chakra-ui/react';
import moment from 'moment-timezone';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/style.css';
import { IoIosArrowRoundBack } from 'react-icons/io';

import CreateFormFromQuestions from '@/components/form/CreateFormFromQuestions';
import { useBooking } from '../_hook/useBooking';
import PaymentSection from './PaymentSection';

export default function BookingForm({
  userDetails,
  eventData,
  organizationDetails,
}: any) {
  const useBookingHook = useBooking({
    userDetails,
    eventData,
    organizationDetails,
  });

  console.log('eventdata--4', eventData);
  const {
    // handleCheckout,
    handleSchedule,
    checkoutLoading,
    scheduleLoading,
    setSelectedDate,
    setSelectedMonth,
    setSelectedTime,
    selectedDate,
    selectedTime,
    isFormPage,
    isLoading,
    availableDays,
    blueColor,
    setIsFormpage,
    timeSlots,
    acceptPayment,
    handlePayment,
  } = useBookingHook;
  if (!eventData) {
    return <Box>No form</Box>;
  }

  //console.log('eventData is ', eventData);

  return (
    <Box w={'100%'} h={'100%'} pt={{ lg: '1.5rem' }}>
      {!isFormPage && (
        <Flex
          flexDirection={{ lgDown: 'column' }}
          justifyContent={'space-between'}
          h={'100%'}
          gap={'2rem'}
        >
          <Box pos={'relative'}>
            {isLoading && (
              <Box zIndex={2} pos="absolute" inset="0" bg="bg/80">
                <Center h="20rem">
                  <Spinner color="blue.500" />
                </Center>
              </Box>
            )}
            <DayPicker
              mode="single"
              selected={selectedDate}
              onMonthChange={(month: any) => {
                //console.log(month);

                setSelectedMonth(month);
                setSelectedDate(null);
                setSelectedTime(null);
              }}
              onSelect={(date: any) => {
                if (date) {
                  //console.log('date   is ', date);

                  setSelectedDate(date);
                } else {
                  setSelectedDate(null);
                }
                setSelectedTime(null);
              }}
              disabled={[
                { before: new Date() },
                (date: any) =>
                  !availableDays?.some(
                    (availableDate: any) =>
                      availableDate.toDateString() === date.toDateString()
                  ),
              ]}
              modifiers={{ available: availableDays }}
              modifiersStyles={{
                available: {
                  background: 'rgba(0, 105, 255, 0.065)',
                  borderRadius: 100,
                  color: blueColor,
                  width: 10,
                },
              }}
            />
            <Flex
              border={'1px solid'}
              borderColor={'rgba(0,0,0,0.4)'}
              rounded={'.3rem'}
              p={'.5rem'}
              alignItems={'center'}
              mt={'2rem'}
              gap={'1rem'}
            >
              <Text color={'gray.300'}>Timezone:</Text>

              <Text fontSize={'1.15rem'}>
                {Intl.DateTimeFormat().resolvedOptions().timeZone}
              </Text>
            </Flex>
            <Button
              hideBelow={'lg'}
              bottom={'2rem'}
              pos={'absolute'}
              color={'white'}
              bg={'rgba(0,0,0,0.8)'}
              w={'10rem'}
              disabled={!selectedDate || !selectedTime}
              onClick={() => setIsFormpage(true)}
            >
              Next
            </Button>
          </Box>
          {selectedDate && (
            <Box
              h={'100%'}
              overflowY={'scroll'}
              css={{
                '&::-webkit-scrollbar': {
                  width: '16px', // Make the scrollbar small but still present
                  background: 'transparent', // Hide the scrollbar track
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'rgba(0, 0, 0, 0.2)', // Style the scrollbar thumb
                  borderRadius: '4px',
                },
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgba(0, 0, 0, 0.2) transparent',
              }}
              pr={{ lg: '2rem' }}
            >
              <Text mb="1rem" minW="8rem">
                Available time{' '}
                <Text as="span" color="blue.500" fontWeight="semibold">
                  (Select one)
                </Text>
              </Text>

              <SimpleGrid
                columns={{ base: 3, md: 3, lg: 1 }}
                gap={{ base: '3', lg: '1rem' }}
              >
                {timeSlots?.slots?.map((slot: string) => {
                  const isActive = selectedTime === slot;
                  const userTimeZone =
                    Intl.DateTimeFormat().resolvedOptions().timeZone;

                  // Convert UTC time to local time
                  const localTime = moment
                    .utc(`${timeSlots?.date} ${slot}`, 'YYYY-MM-DD HH:mm')
                    .tz(userTimeZone)
                    .format('h:mm A'); // Use "HH:mm" for 24-hour format

                  return (
                    <Center
                      h={'2.5rem'}
                      w={{ lg: '8rem' }}
                      key={`${timeSlots.date}-${slot}`}
                      cursor={'pointer'}
                      rounded={'.3rem'}
                      borderColor={blueColor}
                      border={isActive ? 'none' : '1px solid'}
                      color={isActive ? 'white' : blueColor}
                      bg={isActive ? blueColor : 'white'}
                      onClick={() => setSelectedTime(slot)}
                      fontSize={'.8rem'}
                    >
                      {localTime}
                    </Center>
                  );
                })}
              </SimpleGrid>
            </Box>
          )}
          <Button
            hideFrom={'lg'}
            color={'white'}
            bg={'rgba(0,0,0,0.8)'}
            w={'full'}
            disabled={!selectedDate || !selectedTime}
            onClick={() => setIsFormpage(true)}
          >
            Next
          </Button>

          {/* <Flex
          justifyContent={'flex-end'}
          position={'sticky'}
          bottom={'1.75rem'}
        >
          <Button mx={'2.5rem'}>Next</Button>
        </Flex> */}
        </Flex>
      )}

      {isFormPage && (
        <Box
          css={{
            '&::-webkit-scrollbar': {
              width: '16px',
              background: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
            },
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(0, 0, 0, 0.2) transparent',
          }}
          overflowY={'scroll'}
          h={'100%'}
          pos={'relative'}
          pb={'1.5rem'}
        >
          <Flex
            onClick={() => setIsFormpage(false)}
            cursor={'pointer'}
            alignItems={'center'}
            gap={'1rem'}
            mb={'1rem'}
          >
            <Center
              minH={'2rem'}
              minW={'2rem'}
              maxW={'2rem'}
              maxH={'2rem'}
              rounded={'100%'}
              border={'1px solid black'}
            >
              <IoIosArrowRoundBack size={'1.5rem'} />
            </Center>

            <Text>Go Back</Text>
          </Flex>

          <Text mb={'1rem'} fontWeight={'semibold'} fontSize={'1.2rem'}>
            Enter Details
          </Text>

          {/* <form onSubmit={handleSchedule}> */}
          <Stack gap={{ lg: '1.5rem' }}>
            <CreateFormFromQuestions
              questions={eventData?.form_config}
              submitHandler={(values: any) => {
                acceptPayment ? handlePayment(values) : handleSchedule(values);
              }}
              submitButton={() => (
                <Button
                  bottom={'0rem'}
                  // pos={'sticky'}
                  color={'white'}
                  bg={'black'}
                  w={'100%'}
                  disabled={!selectedDate || !selectedTime}
                  mt={{ base: '2', lg: '2rem' }}
                  type="submit"
                  loading={scheduleLoading || checkoutLoading}
                >
                  {acceptPayment ? 'Checkout' : 'Schedule Event'}
                </Button>
              )}
              {...(acceptPayment
                ? {
                    extraComponent: (
                      <PaymentSection
                        service={eventData?.service}
                        useBooking={useBookingHook}
                      />
                    ),
                  }
                : {})}
              //  {... (hasProduct?{ extraComponent:{<PaymentSection product={eventData?.product} />}:{}})}
            />
          </Stack>
          {/* </form> */}
        </Box>
      )}
    </Box>
  );
}
