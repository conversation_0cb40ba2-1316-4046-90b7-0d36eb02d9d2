'use client';

import { env } from '@/constants/env';
import { Box } from '@chakra-ui/react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

export default function StripeProvider({ userDetails, children }: any) {
  const stripePromise = loadStripe(String(env.STRIPE_PUBLISHABLE_KEY), {
    stripeAccount: userDetails?.organization?.stripe_user_id,
  });
  return (
    <Box flex={1} h={'100%'}>
      <Elements stripe={stripePromise}>{children}</Elements>
    </Box>
  );
}
