import moment from 'moment-timezone';
import { useState } from 'react';
import { createBooking } from '../_util/schedule-event';
import { toaster } from '@/components/ui/toaster';
import { useRouter } from 'next/navigation';
import { useGetSlotsQuery } from '@/api/availability/get-slots';
import { stripeWebhookType } from '@/constants';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';

export const useBooking = ({
  userDetails,
  eventData,
  organizationDetails,
}: any) => {
  const stripe = useStripe();
  const elements = useElements();

  const router = useRouter();
  const acceptPayment = eventData?.accept_payment;
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState<any>(new Date());
  const [scheduleLoading, setScheduleLoading] = useState(false);
  const [selectedTime, setSelectedTime] = useState<any>(null);
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const [bookingForm, setBookingForm] = useState<any>();
  const [selectedMonth, setSelectedMonth] = useState<any>(
    moment().tz(timeZone).startOf('month').toDate()
  );
  const [isFormPage, setIsFormpage] = useState<any>(false);
  const blueColor = 'rgb(0, 96, 230)';
  const { data, isLoading } = useGetSlotsQuery({
    slug: eventData.slug,
    selectedMonth,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    orgData: { ...organizationDetails, owner: userDetails },
  });
  const availableDays = data
    ?.filter((item: any) => moment(item.date).isSameOrAfter(moment(), 'day'))
    ?.filter((item: any) => {
      return item.slots.length > 0;
    })
    ?.map((item: any) => new Date(`${item.date}T00:00:00`));
  const timeSlots =
    data?.find(
      (item: any) => item.date === moment(selectedDate).format('YYYY-MM-DD')
    ) || [];

  const getAnswerDetails = (values: any) => {
    const questionAnswers = { ...values };
    const answer_details =
      eventData?.form_config?.map((question: any) => ({
        id: question.id,
        qt: question.qt,
        show_answers: 'true',
        ans:
          question.type === 'Multiple choice'
            ? questionAnswers[`question_${question.id}`].join(', ')
            : questionAnswers[`question_${question.id}`] || '',
      })) || [];
    const firstName = answer_details?.find(
      (item: any) => item.qt === 'First name'
    )?.ans;
    const lastName = answer_details?.find(
      (item: any) => item.qt === 'Last name'
    )?.ans;
    const email = answer_details?.find((item: any) => item.qt === 'Email')?.ans;
    const phoneNumber = answer_details?.find(
      (item: any) => item.qt === 'Phone number'
    )?.ans;

    return {
      firstName,
      lastName,
      email,
      phoneNumber,
      answer_details,
    };
  };

  // CREATE BOOKING IN SUPABASE AND GOOGLE
  const handleCreateBooking = async (values: any) => {
    const { firstName, lastName, phoneNumber, email, answer_details } =
      getAnswerDetails(values);

    const res: any = await createBooking(
      {
        // ...form,
        selectedTime,
        selectedDate,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        firstName,
        lastName,
        email,
        phoneNumber,
      },
      { ...organizationDetails, owner: userDetails },
      eventData,
      userDetails
    );

    const formAnswersPayload = {
      booking_id: res.id,
      answer_details,
    };

    const formAnswerRes = await fetch('/api/public/form-answers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formAnswersPayload),
    });
    if (!formAnswerRes.ok) {
      throw new Error(await formAnswerRes.json());
    }

    // router.push(`/book/success/${res.google_event_id}`);
    return res;
  };

  const handleSchedule = async (values: any) => {
    try {
      setScheduleLoading(true);
      const res = await handleCreateBooking(values);
      router.push(`/book/success/${res.google_event_id}`);
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error.message,
      });
    } finally {
      setScheduleLoading(false);
    }
  };

  const handleCheckout = async (values: any) => {
    try {
      setCheckoutLoading(true);

      //console.log('checking out with values', values);
      const { firstName, lastName, phoneNumber, email, answer_details } =
        getAnswerDetails(values);
      const payload = {
        eventData,
        userDetails,
        answer_details,
        selectedTime,
        selectedDate,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        firstName,
        lastName,
        email,
        phoneNumber,
        organizationDetails,
      };

      //console.log('payload is ', payload);

      const rawData = await fetch(
        '/api/public/bookings/save-unprocessed-stripe',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ raw_data: payload }),
        }
      );
      if (!rawData.ok) {
        throw new Error((await rawData.json()).message);
      }

      const raw_data_id = (await rawData.json())?.id;

      const response = await fetch('/api/stripe/generate-payment-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          raw_data_id,
          type: stripeWebhookType.buyServiceThroughBooking,
          slp_id: userDetails.id,
          stripe_user_id: userDetails?.organization?.stripe_user_id,
          organization_id: userDetails?.organization_id,
          service: eventData?.service,
        }),
      });
      if (!response.ok) {
        throw new Error((await response.json()).message);
      }
      //console.log('mo error');

      const data = await response.json();
      if (typeof window !== 'undefined') {
        window.location.href = data.url;
      }
      //console.log('data is ', data.url);
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error.message,
      });
    } finally {
      setCheckoutLoading(false);
    }
  };

  const handlePayment = async (values: any) => {
    try {
      setCheckoutLoading(true);

      // GET ANSWER DETAILS FROM FORM
      const { firstName, lastName, phoneNumber, email } =
        getAnswerDetails(values);

      // VALIDATE STRIPE INPUTS
      if (!stripe || !elements) {
        //console.log('no stripe elements');

        throw new Error('Stripe has not loaded yet.');
      }
      //console.log('trying to get card elements');

      const cardElement = elements.getElement(CardElement);
      if (!cardElement) {
        throw new Error('Card element not found.');
      }
      if (!bookingForm.nameOnCard) {
        throw new Error('Add name on card.');
      }

      // CREATE PAYMENT METHOD ON STRIPE
      setCheckoutLoading(true);

      const { paymentMethod, error } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: bookingForm?.nameOnCard,
          email: values.email,
          phone: values.phoneNumber,
        },
      });
      if (error || !paymentMethod) {
        //console.log('no stripe elements');

        throw new Error(error?.message || 'Card error');
      }

      // CONFIRM PAYMENT
      const res = await fetch('/api/stripe/confirm-payment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stripe_user_id: userDetails?.organization?.stripe_user_id,
          paymentMethodId: paymentMethod.id,
          service: eventData.service,
          organization_id: userDetails?.organization_id,
          customerInfo: {
            firstName: firstName,
            lastName: lastName,
            phoneNumber: phoneNumber,
            email: email,
          },
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        throw new Error(data?.message);
      }
      const resData = await res.json();

      // IF PAYMENT IS SUCCESSFUL
      if (resData?.success) {
        // CREATE BOOKING
        //console.log('intent is ', resData?.intent);
        const res = await handleCreateBooking(values);

        // CREATE TRANSACTION
        const payload = {
          transaction_type: 'PAYMENT',
          status: 'COMPLETE',
          amount: resData?.intent?.amount / 100,
          currency_code: resData?.intent?.currency?.toUpperCase(),
          payment_method: 'CARD',
          reference_id: resData?.intent?.id,
          organization_id: organizationDetails?.id,
          user_id: userDetails?.id,
          transaction_date: new Date(
            resData?.intent?.created * 1000
          ).toISOString(),
          client_id: res?.client_id,
          stripe_intent_id: resData?.intent?.id,
        };
        // console.log('response from creating booking is ', res);
        // console.log('payload to create transaction is', payload);
        const transactionRes = await fetch('/api/transactions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        if (!transactionRes.ok) {
          const data = await transactionRes.json();
          throw new Error(data?.message);
        }
        router.push(`/book/success/${res.google_event_id}`);
      }
      setCheckoutLoading(false);
    } catch (error: any) {
      setCheckoutLoading(false);
      toaster.create({
        type: 'error',
        description: error.message,
      });
    } finally {
      setCheckoutLoading(false);
    }

    // const intentResponse = await fetch('/api/stripe/create-payment-intent', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     stripe_user_id: userDetails?.organization?.stripe_user_id,
    //     product: eventData.product,
    //     organization_id: userDetails?.organization_id,
    //   }),
    // });
    // if (!intentResponse.ok) {
    //   throw new Error((await intentResponse.json()).message);
    // }

    // const intent = await intentResponse.json();

    // const clientSecret = intent.clientSecret;

    // const result = await stripe?.confirmCardPayment(clientSecret, {
    //   payment_method: {
    //     card: cardElement,
    //     billing_details: {
    //       name: `${firstName} ${lastName}`,
    //       email: email,
    //       phone: phoneNumber,
    //     },
    //   },
    // });
    // console.log('result is ', result);

    // if (result?.error) {
    //   alert(result.error.message);
    //   // setLoading(false);
    //   return;
    // }

    // if (result?.paymentIntent?.status === 'succeeded') {
    //   // 3. Call booking API
    //   console.log('payment succeed');
    // }
  };
  return {
    handleCheckout,
    handleSchedule,
    checkoutLoading,
    scheduleLoading,
    setSelectedDate,
    setSelectedMonth,
    setSelectedTime,
    selectedDate,
    selectedMonth,
    selectedTime,
    isFormPage,
    isLoading,
    availableDays,
    blueColor,
    setIsFormpage,
    timeSlots,
    acceptPayment,
    handlePayment,
    bookingForm,
    setBookingForm,
  };
};

export type TUseBooking = ReturnType<typeof useBooking>;
