export const fetchCache = 'force-no-store';

import { getEventsBySlugAndCreator, getUserBuSlug } from '@/app/service/events';
import { env } from '@/constants/env';
import { Box, Flex, Separator } from '@chakra-ui/react';
import { createClient } from '@supabase/supabase-js';
import { notFound } from 'next/navigation';
import BookingForm from './_components/BookingForm';
import EventDetails from './_components/EventDetails';
import StripeProvider from './_components/StripeProvider';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function generateMetadata({ params }: any) {
  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  const eventDetails = await getEventsBySlugAndCreator(
    supabase,
    params.event_slug,
    params.organization_name
  );

  console.log('eventDetails details is ', eventDetails);
  if (!userDetails) {
    return {
      title: 'User Not Found',
    };
  }
  if (!eventDetails) {
    return {
      title: 'Event Not Found',
    };
  }

  return {
    title: `Book ${eventDetails.title} with ${userDetails?.event_slug} | Speakfluent`,
    description: `Schedule a ${eventDetails.duration}-minute ${eventDetails.title} event with ${userDetails?.event_slug}.`,
  };
}
export default async function page({ params }: any) {
  const eventDetails = await getEventsBySlugAndCreator(
    supabase,
    params.event_slug,
    params.organization_name
  );

  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  // const availability = await generateAvailability(supabase, params.event_slug);

  // console.log('organization is ', organizationDetails);

  if (!eventDetails) {
    notFound();
  }
  return (
    <div>
      <Box
        minH={'100vh'}
        pt={'2rem'}
        pb={{ lgDown: '2rem' }}
        px={{ lgDown: '4' }}
      >
        <Box
          rounded={{ lg: '.5rem' }}
          boxShadow={{ lg: 'lg' }}
          maxW={'65rem'}
          mx={'auto'}
          h={{ lg: '85vh' }}
          bg={'white'}
          overflowY={'auto'}
        >
          <Flex
            flexDirection={{ base: 'column', lg: 'row' }}
            h={'100%'}
            pl={{ lg: '3rem' }}
            pr={{ lg: '1rem' }}
            gap={{ base: '1rem', lg: '3rem' }}
          >
            <Box w={{ lg: '20rem' }}>
              <EventDetails
                organizationDetails={userDetails?.organization}
                eventData={eventDetails}
              />
            </Box>
            <Separator
              borderColor={'rgba(0,0,0,0.15)'}
              orientation={{ base: 'horizontal', lg: 'vertical' }}
              h={'100%'}
            />
            <Box flex={1}>
              <StripeProvider userDetails={userDetails}>
                <BookingForm
                  userDetails={userDetails}
                  eventData={eventDetails}
                  organizationDetails={userDetails?.organization}
                />
              </StripeProvider>
            </Box>
          </Flex>
        </Box>
      </Box>
    </div>
  );
}
