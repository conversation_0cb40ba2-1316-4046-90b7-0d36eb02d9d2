import moment from 'moment';

export const getOrganization = async (eventData: any) => {
  // console.log(eventData);
  //console.log('trying to fetch organizatiopn');

  const orgRes = await fetch(
    `/api/public/organizations/${eventData.organization_id}`
  );
  // console.log('org res is ', orgRes);

  const orgData = await orgRes.json();
  // console.log('Organization data is ', orgData);

  if (!orgRes.ok) {
    throw new Error(orgData.message || 'Failed to fetch organization details');
  }

  return orgData;
};

export const getClient = async (form: any, orgData: any) => {
  const clientRes = await fetch(
    `/api/public/clients/email/${form.email}/${orgData.id}`
  );
  let clientData = await clientRes.json();
  //console.log('client res is ', clientData);
  // return;

  // console.log('Organization data is ', orgData);

  if (!clientRes.ok) {
    throw new Error(clientData.message || 'Failed to fetch client details');
  }
  if (clientData) {
    return { ...clientData, id: clientData.client_id };
  }

  const client_obj: any = {
    // email: form.email.toLowerCase(),
    organization_id: orgData.id,
    first_name: form.firstName,
    last_name: form.lastName,
    display_name: `${form.firstName} ${form.lastName}`,
    phone: form.phoneNumber,
    province: form.province,
    lead_created: new Date(),
    referral_source: form.referralSource,
    last_consultation_date: moment(form.selectedDate)
      .utc()
      .set({
        hour: Number(form?.selectedTime.split(':')[0]),
        minute: Number(form?.selectedTime.split(':')[1]),
        second: 0,
      })
      .toDate(),
  };

  const createClientRes = await fetch('/api/public/clients', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(client_obj),
  });
  clientData = await createClientRes.json();

  //console.log('clientData', clientData);

  if (!createClientRes.ok) {
    throw new Error(clientData.message || 'Failed to create client details');
  }

  // Create Client Emails
  const createClientEmailsRes = await fetch('/api/public/client-emails', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      client_id: clientData.id,
      email: form.email.toLowerCase(),
      is_primary_email: true,
      organization_id: orgData.id,
    }),
  });
  const clientEmailData = await createClientEmailsRes.json();

  if (!createClientEmailsRes.ok) {
    throw new Error(
      clientEmailData.message || 'Failed to create client emails'
    );
  }

  // update client activity when a new client is created.
  const createClientActivityRes = await fetch(
    '/api/public/clients-activities',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_id: clientData.id,
        organization_id: orgData.id,
        activity_type: 'client_created',
        details: { created_by: 'scheduler' },
      }),
    }
  );
  const clientActivityData = await createClientActivityRes.json();

  if (!createClientActivityRes.ok) {
    throw new Error(
      clientActivityData.message || 'Failed to create client activity'
    );
  }

  return clientData;
};

export const generateMeetLink = async (
  form: any,
  orgData: any,
  eventData: any,
  organizer_email: any
) => {
  const calendarRes = await fetch('/api/public/calendar', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ form, orgData, eventData, organizer_email }),
  });
  const calendarData = await calendarRes.json();

  if (!calendarRes.ok) {
    throw new Error(orgData.message || 'Failed to generate meet link');
  }
  return calendarData;
};

export const createBooking = async (
  form: any,
  orgData: any,
  eventData: any,
  userDetails: any
) => {
  if (!form || !eventData || !orgData) {
    throw new Error('Invalid params');
  }
  //console.log('Typing to create booking');
  // console.log('form is ', fn
  const client = await getClient(form, orgData);
  //console.log('client is ', client);

  // Schedule Event On Google
  const { data: googleEventData } = await generateMeetLink(
    form,
    orgData,
    eventData,
    userDetails?.email
  );
  // return;
  // console.log('client is ', client);

  const payload = {
    first_name: form?.firstName,
    last_name: form?.lastName,
    email: form?.email,
    phone: form.phoneNumber,
    province: form.province,
    booking_created_at_raw: new Date(),
    event: eventData?.title,
    calendly_event_type: 'invitee.created',
    assigned_to: userDetails?.organizer_email || orgData?.owner?.email,
    appointment_raw: moment(form.selectedDate)
      .local()
      .set({
        hour: Number(form?.selectedTime.split(':')[0]),
        minute: Number(form.selectedTime.split(':')[1]),
        second: 0,
      })
      .format('M/D/YYYY HH:mm:ss'),
    client_id: client.id,
    product: eventData?.title,
    created_at: new Date(),
    appointment: googleEventData?.start?.dateTime,
    slp_id: userDetails?.id || orgData?.owner.id,
    organization_id: orgData?.id,
    event_id: eventData.id,
    start_time: googleEventData?.start?.dateTime,
    end_time: googleEventData?.end?.dateTime,
    time_zone: googleEventData?.start?.timeZone,
    google_event_id: googleEventData.id,
    service_id: eventData?.service_id,
  };

  //console.log('payload is ', payload);
  // return;

  const bookingRes = await fetch('/api/public/bookings', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
  const bookingData = await bookingRes.json();
  //console.log('bookingData data is ', bookingData);

  if (!bookingRes.ok) {
    throw new Error(orgData.message || 'Failed to fetch create booking');
  }

  return bookingData;
  // return bookingData;
};
