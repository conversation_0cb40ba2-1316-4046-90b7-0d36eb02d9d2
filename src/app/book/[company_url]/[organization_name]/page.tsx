import {
  getEventsByCreatorSlug,
  // getOrganizationByName,
  // getEventsByOrganizationName,
  getUserBuSlug,
} from '@/app/service/events';
import sfLogo from '@/assets/logo.png';
import { env } from '@/constants/env';
import { IEvent } from '@/shared/interface/events';
import {
  Box,
  Center,
  GridItem,
  SimpleGrid,
  Stack,
  Text,
} from '@chakra-ui/react';
import { createClient } from '@supabase/supabase-js';
import { unstable_noStore as noStore } from 'next/cache';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function generateMetadata({ params }: any) {
  noStore();
  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  // console.log('user details is ', userDetails);

  if (!userDetails) {
    return {
      title: 'User Not Found',
    };
  }

  return {
    title: `${userDetails.event_slug}'s Profile | Soap`,
    description: `Book an event with ${userDetails.event_slug}. View available public events and schedules.`,
    openGraph: {
      title: `${userDetails.event_slug}'s Profile | Soap`,
      description: `Book an event with ${userDetails.event_slug}. View available public events and schedules.`,
      url: `https://app.soapnotes.online/client/${params.organization_name}/${userDetails.event_slug}`,
      siteName: 'Soap Dashboard',
      images: [
        {
          url: 'https://app.soapnotes.online/og-image.png',
          width: 1200,
          height: 630,
          alt: 'Soap Invoice Page',
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${userDetails.event_slug}'s Profile | Speakfluent`,
      description: `Book an event with ${userDetails.event_slug}. View available public events and schedules.`,
      images: ['https://app.soapnotes.online/twitter-image.png'],
    },
    metadataBase: new URL('https://app.soapnotes.online'),
  };
}
export default async function page({ params }: any) {
  // const organizationDetails = await getOrganizationByName(
  //   supabase,
  //   params.organization_name
  // );
  const userDetails = await getUserBuSlug(supabase, params.organization_name);

  const events = await getEventsByCreatorSlug(
    supabase,
    params.organization_name
  );

  console.log('events are ', events);
  if (!userDetails) {
    return notFound();
  }
  return (
    <Box minH={'100vh'} pt={'2rem'}>
      {/* This will show the bookings of the organization {params.organization_name} */}
      <Box
        rounded={'.5rem'}
        boxShadow={'lg'}
        maxW={'65rem'}
        mx={'auto'}
        minH={'85vh'}
      >
        <Center pt={'1.5rem'}>
          <Image
            alt={userDetails?.organization?.name}
            src={userDetails?.organization?.logo_url || sfLogo.src}
            width={150}
            height={150}
          />
        </Center>
        <Text
          mt={'.5rem'}
          textAlign={'center'}
          color={'gray.400'}
          fontWeight={'semibold'}
          textTransform={'capitalize'}
        >
          {userDetails?.event_slug}
        </Text>
        {events?.length === 0 ? (
          <Box
            mt={'2rem'}
            textAlign={'center'}
            fontWeight={600}
            fontSize={'1.2rem'}
          >
            No openings at the moment.
          </Box>
        ) : (
          <SimpleGrid
            p={'1rem'}
            gap={'2rem'}
            mt={'.5rem'}
            columns={{ base: 1, md: 3 }}
          >
            {events?.map((item: IEvent) => {
              const url = `${env.FRONTEND_URL}/book/${item.creator_slug}/${item.slug}`;
              return (
                <GridItem key={item.id}>
                  <Link href={url}>
                    <Stack
                      p={'1rem'}
                      // border={'1px solid rgba(0,0,0,0.05)'}
                      // bg={}
                      boxShadow={'sm'}
                      _hover={{ bg: 'rgba(0,0,0,0.05)' }}
                      minH={'10rem'}
                      rounded={'.5rem'}
                      gap={'0'}
                      justifyContent={'space-between'}
                      cursor={'pointer'}
                    >
                      <Box>
                        <Text fontWeight={'600'} fontSize={'1.2rem'}>
                          {item?.title}
                        </Text>
                        <Text
                          color={'gray.300'}
                          fontSize={'.75rem'}
                          mt={'.3rem'}
                        >
                          {item.duration} minutes
                        </Text>

                        <Text
                          fontSize={'.85rem'}
                          mt={'1rem'}
                          color={'gray.500'}
                        >
                          {item.description}
                        </Text>
                      </Box>
                    </Stack>
                  </Link>
                </GridItem>
              );
            })}
          </SimpleGrid>
        )}
      </Box>
    </Box>
  );
}
