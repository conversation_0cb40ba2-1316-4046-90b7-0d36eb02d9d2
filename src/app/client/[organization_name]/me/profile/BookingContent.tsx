'use client';

import { <PERSON>, Card, Badge, Flex, VStack, Text } from '@chakra-ui/react';

const BookingContent = () => {
  return (
    <Card.Root borderColor={'gray.50'}>
      <Card.Header gap={'0'} px={{ base: '3', md: '6' }}>
        <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>My Bookings</Card.Title>
        <Card.Description color="#6b7280" fontSize={{ base: 'sm', lg: 'md' }}>
          View and manage your session bookings
        </Card.Description>
      </Card.Header>
      <Card.Body px={{ base: '3', md: '6' }}>
        <VStack gap={4} align="stretch">
          <Box
            p={4}
            borderWidth="1px"
            borderColor={'#e5e7eb'}
            borderRadius="md"
          >
            <Flex
              justify="space-between"
              flexDirection={{ base: 'column', md: 'row' }}
              align={{ md: 'center' }}
              gap={'4'}
            >
              <Box>
                <Text fontSize={'md'} fontWeight={'600'}>
                  Yoga Session with Sarah
                </Text>
                <Text textStyle="sm" color="#4b5563">
                  Tomorrow, 10:00 AM - 11:00 AM
                </Text>
              </Box>
              <Badge
                rounded={'full'}
                colorPalette="green"
                variant={'surface'}
                fontWeight={'600'}
                px={'3'}
                w={'fit'}
              >
                Confirmed
              </Badge>
            </Flex>
          </Box>
          <Box
            p={4}
            borderWidth="1px"
            borderColor={'#e5e7eb'}
            borderRadius="md"
          >
            <Flex
              justify="space-between"
              flexDirection={{ base: 'column', md: 'row' }}
              align={{ md: 'center' }}
              gap={'4'}
            >
              <Box>
                <Text fontSize={'md'} fontWeight={'600'}>
                  Personal Training with Mike
                </Text>
                <Text textStyle="sm" color="#4b5563">
                  Dec 20, 2024, 3:00 PM - 4:00 PM
                </Text>
              </Box>
              <Badge
                rounded={'full'}
                colorPalette="orange"
                variant={'surface'}
                fontWeight={'600'}
                px={'3'}
                w={'fit'}
              >
                Pending
              </Badge>
            </Flex>
          </Box>
        </VStack>
      </Card.Body>
    </Card.Root>
  );
};

export default BookingContent;
