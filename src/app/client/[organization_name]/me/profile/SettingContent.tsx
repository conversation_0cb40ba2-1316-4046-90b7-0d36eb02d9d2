'use client';

import {
  <PERSON>,
  Card,
  Flex,
  VStack,
  HStack,
  Text,
  IconButton,
} from '@chakra-ui/react';
import { FiTrash2, FiShield, FiBell } from 'react-icons/fi';
import { Button } from '@/components/ui/button';

const SettingContent = () => {
  return (
    <Card.Root borderColor={'gray.50'}>
      <Card.Header gap={'0'} px={{ base: '3', md: '6' }}>
        <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
          Account Settings
        </Card.Title>
        <Card.Description color="#6b7280" fontSize={{ base: 'sm', lg: 'md' }}>
          Manage your account preferences and security
        </Card.Description>
      </Card.Header>
      <Card.Body px={{ base: '3', md: '6' }}>
        <VStack gap={6} align="stretch">
          <Flex
            justify="space-between"
            align={{ lg: 'center' }}
            border="1px solid"
            borderColor={'#e5e7eb'}
            rounded={'md'}
            p={'4'}
            flexDirection={{ base: 'column', sm: 'row' }}
            gap={'4'}
          >
            <HStack gap={3}>
              <IconButton
                size="md"
                border={'none'}
                color={'blue.600'}
                bg={'blue.100'}
              >
                <FiShield size={20} />
              </IconButton>
              <Box>
                <Text fontSize={'md'} fontWeight={'600'}>
                  Two-Factor Authentication
                </Text>
                <Text textStyle="sm" color="#4b5563">
                  Add an extra layer of security
                </Text>
              </Box>
            </HStack>
            <Button
              variant="outline"
              fontWeight={'600'}
              borderColor={'#d1d5db'}
              _hover={{ bg: '#f3f4f6', color: 'black' }}
            >
              Enable
            </Button>
          </Flex>
          <Flex
            justify="space-between"
            align={{ lg: 'center' }}
            border="1px solid"
            borderColor={'#e5e7eb'}
            rounded={'md'}
            p={'4'}
            flexDirection={{ base: 'column', sm: 'row' }}
            gap={'4'}
          >
            <HStack gap={3}>
              <IconButton
                size="md"
                border={'none'}
                color={'green.600'}
                bg={'green.100'}
              >
                <FiBell size={20} />
              </IconButton>
              <Box>
                <Text fontSize={'md'} fontWeight={'600'}>
                  Email Notifications
                </Text>
                <Text textStyle="sm" color="#4b5563">
                  Receive booking confirmations and updates
                </Text>
              </Box>
            </HStack>
            <Button
              variant="outline"
              fontWeight={'600'}
              borderColor={'#d1d5db'}
              _hover={{ bg: '#f3f4f6', color: 'black' }}
            >
              Manage
            </Button>
          </Flex>
          <Flex
            justify="space-between"
            align={{ lg: 'center' }}
            border="1px solid"
            rounded={'md'}
            p={'4'}
            flexDirection={{ base: 'column', sm: 'row' }}
            gap={'4'}
            borderColor={'red.100'}
            bg={'red.50'}
          >
            <HStack gap={3}>
              <IconButton
                size="md"
                border={'none'}
                color={'red.600'}
                bg={'red.100'}
              >
                <FiTrash2 size={20} />
              </IconButton>
              <Box>
                <Text fontSize={'md'} fontWeight={'600'} color={'red.700'}>
                  Delete Account
                </Text>
                <Text textStyle="sm" color={'red.500'}>
                  Permanently delete your account and data
                </Text>
              </Box>
            </HStack>
            <Button colorPalette="red">Delete</Button>
          </Flex>
        </VStack>
      </Card.Body>
    </Card.Root>
  );
};

export default SettingContent;
