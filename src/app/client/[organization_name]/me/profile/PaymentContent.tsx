'use client';

import { useState } from 'react';
import {
  Box,
  Card,
  Input,
  Field,
  Select,
  Badge,
  Flex,
  VStack,
  HStack,
  Text,
  Grid,
  GridItem,
  IconButton,
} from '@chakra-ui/react';
import { FiCreditCard, FiPlus, FiTrash2 } from 'react-icons/fi';
import { Button } from '@/components/ui/button';

interface PaymentCard {
  id: string;
  type: string;
  last4: string;
  expiryMonth: string;
  expiryYear: string;
  holderName: string;
  isDefault: boolean;
}

const PaymentContent = () => {
  const [cards, setCards] = useState<PaymentCard[]>([
    {
      id: '1',
      type: 'Visa',
      last4: '4242',
      expiryMonth: '12',
      expiryYear: '2025',
      holderName: '<PERSON>',
      isDefault: true,
    },
    {
      id: '2',
      type: 'Mastercard',
      last4: '8888',
      expiryMonth: '06',
      expiryYear: '2026',
      holderName: '<PERSON>',
      isDefault: false,
    },
  ]);

  const [showAddCard, setShowAddCard] = useState(false);
  const [newCard, setNewCard] = useState({
    holderName: '',
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvc: '',
  });

  const handleAddCard = () => {
    if (
      newCard.holderName &&
      newCard.cardNumber &&
      newCard.expiryMonth &&
      newCard.expiryYear &&
      newCard.cvc
    ) {
      const cardType = newCard.cardNumber.startsWith('4')
        ? 'Visa'
        : 'Mastercard';
      const last4 = newCard.cardNumber.slice(-4);

      const card: PaymentCard = {
        id: Date.now().toString(),
        type: cardType,
        last4,
        expiryMonth: newCard.expiryMonth,
        expiryYear: newCard.expiryYear,
        holderName: newCard.holderName,
        isDefault: cards.length === 0,
      };

      setCards([...cards, card]);
      setNewCard({
        holderName: '',
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvc: '',
      });
      setShowAddCard(false);
    }
  };

  const handleRemoveCard = (cardId: string) => {
    setCards(cards.filter((card) => card.id !== cardId));
  };

  const handleSetDefault = (cardId: string) => {
    setCards(
      cards.map((card) => ({
        ...card,
        isDefault: card.id === cardId,
      }))
    );
  };

  return (
    <VStack gap={6} align="stretch">
      <Card.Root borderColor={'gray.50'}>
        <Card.Header px={{ base: '3', md: '6' }}>
          <Flex
            justify="space-between"
            flexDirection={{ base: 'column', md: 'row' }}
            align="start"
            gap={'2'}
          >
            <Box>
              <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
                Payment Methods
              </Card.Title>
              <Card.Description
                color="#6b7280"
                fontSize={{ base: 'sm', lg: 'md' }}
              >
                Manage your saved payment cards
              </Card.Description>
            </Box>
            <Button
              bg={'#e97a5b'}
              _hover={{ bg: 'orange.600' }}
              onClick={() => setShowAddCard(true)}
            >
              <FiPlus size={16} />
              Add Card
            </Button>
          </Flex>
        </Card.Header>
        <Card.Body px={{ base: '3', md: '6' }}>
          {cards.length === 0 ? (
            <VStack gap={4} py={8}>
              <FiCreditCard size={48} color="gray" />
              <Text color="fg.muted">No payment methods added yet</Text>
            </VStack>
          ) : (
            <VStack gap={4} align="stretch">
              {cards.map((card) => (
                <Box
                  key={card.id}
                  p={4}
                  borderWidth="1px"
                  borderColor={'#e5e7eb'}
                  borderRadius="md"
                >
                  <Flex
                    justify="space-between"
                    flexDirection={{ base: 'column', md: 'row' }}
                    align={{ md: 'center' }}
                    gap={'4'}
                  >
                    <HStack gap={4}>
                      <Flex
                        w={12}
                        h={8}
                        bg="#2563eb"
                        //borderRadius="md"
                        align="center"
                        justify="center"
                        color={'white'}
                      >
                        <FiCreditCard size={18} />
                      </Flex>
                      <Box>
                        <HStack gap={2}>
                          <Text fontSize={'sm'} fontWeight={'600'}>
                            {card.type} •••• {card.last4}
                          </Text>
                          {card.isDefault && (
                            <Badge
                              colorPalette="blue"
                              px={'3'}
                              fontWeight={'600'}
                              rounded={'full'}
                            >
                              Default
                            </Badge>
                          )}
                        </HStack>
                        <Text textStyle="sm" color="#4b5563">
                          Expires {card.expiryMonth}/{card.expiryYear} •{' '}
                          {card.holderName}
                        </Text>
                      </Box>
                    </HStack>
                    <HStack gap={2} justifyContent={'end'}>
                      {!card.isDefault && (
                        <Button
                          variant="outline"
                          size="sm"
                          fontWeight={'600'}
                          borderColor={'#d1d5db'}
                          _hover={{ bg: '#f3f4f6', color: 'black' }}
                          onClick={() => handleSetDefault(card.id)}
                        >
                          Set Default
                        </Button>
                      )}
                      <IconButton
                        variant="outline"
                        size="sm"
                        border={'none'}
                        bg={{ base: 'red.100', md: 'transparent' }}
                        color={'red.500'}
                        _hover={{ bg: 'red.100' }}
                        aria-label="Remove card"
                        onClick={() => handleRemoveCard(card.id)}
                      >
                        <FiTrash2 size={16} />
                      </IconButton>
                    </HStack>
                  </Flex>
                </Box>
              ))}
            </VStack>
          )}
        </Card.Body>
      </Card.Root>

      {/* Add Card Form */}
      {showAddCard && (
        <Card.Root>
          <Card.Header>
            <Card.Title>Add New Card</Card.Title>
            <Card.Description>
              Enter your card details to add a new payment method
            </Card.Description>
          </Card.Header>
          <Card.Body>
            <VStack gap={4} align="stretch">
              <Field.Root>
                <Field.Label>Cardholder Name</Field.Label>
                <Input
                  placeholder="John Doe"
                  value={newCard.holderName}
                  onChange={(e) =>
                    setNewCard({
                      ...newCard,
                      holderName: e.target.value,
                    })
                  }
                />
              </Field.Root>
              <Field.Root>
                <Field.Label>Card Number</Field.Label>
                <Input
                  placeholder="1234 5678 9012 3456"
                  value={newCard.cardNumber}
                  onChange={(e) =>
                    setNewCard({
                      ...newCard,
                      cardNumber: e.target.value.replace(/\s/g, ''),
                    })
                  }
                />
              </Field.Root>
              <Grid templateColumns="repeat(3, 1fr)" gap={4}>
                <GridItem>
                  <Field.Root>
                    <Field.Label>Month</Field.Label>
                    <Select.Root
                      value={[newCard.expiryMonth]}
                      onValueChange={(e) =>
                        setNewCard({
                          ...newCard,
                          expiryMonth: e.value[0],
                        })
                      }
                      collection={[] as any}
                    >
                      <Select.Trigger>
                        <Select.ValueText placeholder="MM" />
                      </Select.Trigger>
                      <Select.Content>
                        {Array.from({ length: 12 }, (_, i) => (
                          <Select.Item
                            key={i + 1}
                            item={String(i + 1).padStart(2, '0')}
                          >
                            {String(i + 1).padStart(2, '0')}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select.Root>
                  </Field.Root>
                </GridItem>
                <GridItem>
                  <Field.Root>
                    <Field.Label>Year</Field.Label>
                    <Select.Root
                      value={[newCard.expiryYear]}
                      onValueChange={(e) =>
                        setNewCard({
                          ...newCard,
                          expiryYear: e.value[0],
                        })
                      }
                      collection={[] as any}
                    >
                      <Select.Trigger>
                        <Select.ValueText placeholder="YYYY" />
                      </Select.Trigger>
                      <Select.Content>
                        {Array.from({ length: 10 }, (_, i) => (
                          <Select.Item key={2024 + i} item={String(2024 + i)}>
                            {2024 + i}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select.Root>
                  </Field.Root>
                </GridItem>
                <GridItem>
                  <Field.Root>
                    <Field.Label>CVC</Field.Label>
                    <Input
                      placeholder="123"
                      maxLength={4}
                      value={newCard.cvc}
                      onChange={(e) =>
                        setNewCard({ ...newCard, cvc: e.target.value })
                      }
                    />
                  </Field.Root>
                </GridItem>
              </Grid>
              <HStack gap={2} pt={4}>
                <Button colorPalette="blue" onClick={handleAddCard}>
                  Add Card
                </Button>
                <Button variant="outline" onClick={() => setShowAddCard(false)}>
                  Cancel
                </Button>
              </HStack>
            </VStack>
          </Card.Body>
        </Card.Root>
      )}
    </VStack>
  );
};

export default PaymentContent;
