import ResolveInvoicePayment from './ResolveInvoicePayment';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/constants/env';
import { getPaymentLinkDetails } from '@/app/service/payment-link';
import { stripe } from '@/lib/stripe';
import { getRobustInvoiceById } from '@/app/service/invoice';
import { getUserDetailsById } from '@/app/service/user';
import StripeProvider from './StripeProvider';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export default async function page({ params }: any) {
  const paymentLink = await getPaymentLinkDetails(params.id, supabaseAdmin);
  const invoiceDetails = await getRobustInvoiceById(
    paymentLink.invoice_id,
    supabaseAdmin
  );
  const userDetails = await getUserDetailsById(
    paymentLink.user_id,
    supabaseAdmin
  );

  if (!paymentLink) {
    return <div>Invalid or expired payment link.</div>;
  }

  // Fetch PaymentIntent from Stripe
  const paymentIntent = await stripe.paymentIntents.retrieve(
    paymentLink.payment_intent_id
    // { stripeAccount: paymentLink.stripe_user_id }
  );

  if (paymentIntent.status !== 'requires_payment_method') {
    return <div>Payment already processed or invalid status.</div>;
  }

  const clientSecret = paymentIntent.client_secret!;
  return (
    <div style={{ backgroundColor: '#f4f5f8', minHeight: '100vh' }}>
      <StripeProvider>
        <ResolveInvoicePayment
          id={params.id}
          clientSecret={clientSecret}
          paymentLink={paymentLink}
          invoiceDetails={invoiceDetails}
          userDetails={userDetails}
          paymentIntent={paymentIntent}
        />
      </StripeProvider>
    </div>
  );
}
