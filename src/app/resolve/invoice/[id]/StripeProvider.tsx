'use client';

import { env } from '@/constants/env';
import { Box } from '@chakra-ui/react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

export default function StripeProvider({ children }: any) {
  const stripePromise = loadStripe(String(env.STRIPE_PUBLISHABLE_KEY));
  return (
    <Box flex={1} h={'100%'}>
      <Elements stripe={stripePromise}>{children}</Elements>
    </Box>
  );
}
