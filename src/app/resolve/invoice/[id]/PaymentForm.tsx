'use client';

import StringInput from '@/components/Input/StringInput';
import { Box, Button, Stack, Text } from '@chakra-ui/react';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function PaymentForm({
  clientSecret,
  paymentIntent,
}: {
  clientSecret: string;
  paymentIntent: any;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();

  const [form, setForm] = useState<any>();
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('submitting ', elements);

    if (!stripe || !elements) return;

    setProcessing(true);

    const { error } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement)!,
      },
    });

    if (error) {
      setError(error.message!);
      setProcessing(false);
    } else {
      setProcessing(false);
      router.replace('/resolve/invoice/success');

      console.log('success payment webhook sent');

      // Success, redirect
    }
  };
  return (
    <form onSubmit={handleSubmit}>
      <Stack pb={'1rem'} gap={'1rem'} my={'1rem'}>
        <StringInput
          fieldProps={{ required: true, label: 'Name On Card' }}
          inputProps={{
            value: form?.name,
            onChange: (e) =>
              setForm((prev: any) => ({
                ...prev,
                name: e.target?.value,
              })),
            fontSize: '1rem',
          }}
        />{' '}
        <Box
          py={'.625rem'}
          px={'.5rem'}
          border={'1px solid #636D79'}
          rounded={'.25rem'}
        >
          <CardElement
            options={{
              style: {
                base: {
                  color: '#1a1a1a', // Text color
                  fontFamily:
                    '"Inter", -apple-system, BlinkMacSystemFont, sans-serif', // Modern font stack
                  '::placeholder': {
                    color: '#6b7280', // Placeholder color (gray-500)
                  },
                  padding: '12px', // Inner padding
                },
                invalid: {
                  color: '#dc2626', // Red for errors
                  iconColor: '#dc2626',
                },
              },
            }}
          />
        </Box>
        {error && <Text color={'red.500'}>{error}</Text>}
        <Button
          textTransform={'uppercase'}
          fontWeight={'500'}
          disabled={processing}
          w={'100%'}
          mt={'1rem'}
          fontSize={'1.125rem'}
          loading={processing}
          type="submit"
        >
          Pay {paymentIntent?.currency}${paymentIntent.amount / 100}
        </Button>
        <Text fontSize={'.75rem'} color={'gray.200'}>
          © 2025 Intuit Inc. All rights reserved.
        </Text>
      </Stack>
    </form>
  );
}
