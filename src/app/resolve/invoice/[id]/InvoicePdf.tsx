'use client';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { PDFGenerator } from '@/components/elements/pdf/PDF-Generator';
import { Box } from '@chakra-ui/react';
import { PDFViewer } from '@react-pdf/renderer';
import moment from 'moment';
import { useMemo } from 'react';

export default function InvoicePdf({
  invoiceDetails,
  open,
  setOpen,
}: {
  invoiceDetails: any;
  setOpen: any;
  open: boolean;
}) {
  const totalPaid = invoiceDetails?.transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );
  const amountDue = Number(invoiceDetails?.total_price) - Number(totalPaid);
  const resolvedAmountDue = useMemo(() => {
    if (amountDue < 0) {
      return `(${formatMoney(Math.abs(amountDue), {
        currencyCode: invoiceDetails?.currency_code,
      })})`;
    }
    return formatMoney(amountDue, {
      currencyCode: invoiceDetails?.currency_code,
    });
  }, [amountDue, invoiceDetails?.currency_code]);

  if (open) {
    return (
      <div>
        <Box
          h={'100vh'}
          shadow={'md'}
          overflow={'hidden'}
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="rgba(0,0,0,0.4)"
          zIndex={1000}
          py={'2rem'}
        >
          <Box
            position="absolute"
            top={2}
            right={12}
            cursor="pointer"
            onClick={() => setOpen(false)}
            px={2}
            py={'1'}
            borderRadius="md"
            _hover={{ bg: 'gray.300' }}
            color={'white'}
            fontWeight={700}
          >
            X
          </Box>
          <PDFViewer
            style={{
              height: '100%',
              width: '80%',
              margin: '0 auto',
              border: 'none',
            }}
            showToolbar={true}
          >
            <PDFGenerator
              name={String(
                invoiceDetails?.client?.display_name || invoiceDetails?.name
              )}
              email={String(
                invoiceDetails?.client?.client_emails?.[0]?.email ||
                  invoiceDetails?.email
              )}
              receiptNumber={String(invoiceDetails?.invoice_number)}
              transactions={invoiceDetails?.transactions ?? []}
              date={String(
                moment(invoiceDetails?.invoice_date?.split('T')[0]).format(
                  'MMMM D, YYYY'
                )
              )}
              dueDate={String(
                moment(
                  invoiceDetails?.due_date?.split('T')[0] ||
                    invoiceDetails?.invoice_date?.split('T')[0]
                ).format('MMMM D, YYYY')
              )}
              resolvedAmountDue={resolvedAmountDue}
              amountDue={amountDue}
              activity={String(invoiceDetails?.product || '')}
              invoice={invoiceDetails}
              referral={invoiceDetails?.referral ?? ''}
              quantity={Number(invoiceDetails?.qty || 0)}
              rate={Number(invoiceDetails?.total_price || 0)}
              balance={0}
              memo={String(invoiceDetails?.memo || '')}
            />
          </PDFViewer>
        </Box>
      </div>
    );
  }
  return null;
}
