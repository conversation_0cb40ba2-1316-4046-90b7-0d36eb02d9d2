'use client';

import {
  Box,
  Center,
  //   Flex,
  Grid,
  Heading,
  //   Stack,
  Text,
} from '@chakra-ui/react';
import { FaCheckCircle } from 'react-icons/fa';
// import Link from 'next/link';

// function Item({ label, value }: { label: string; value: string }) {
//   return (
//     <Flex
//       color="gray.600"
//       fontWeight={500}
//       alignItems="center"
//       justifyContent="space-between"
//       gap="1rem"
//     >
//       <Text>{label}</Text>
//       <Text fontWeight={600}>{value}</Text>
//     </Flex>
//   );
// }

export default function PaymentSuccessPage() {
  return (
    <Grid placeContent={'center'} minH="100vh" bg="gray.50">
      <Box
        rounded="lg"
        boxShadow="md"
        maxW="40rem"
        mx="auto"
        bg="white"
        p="2rem"
      >
        {/* Success Icon & Message */}
        <Center flexDirection="column" gap="1rem" textAlign="center">
          <FaCheckCircle size={40} color="green" />
          <Heading fontSize="1.5rem">Payment Successful 🎉</Heading>
          <Text fontSize="sm" color="gray.600">
            Thank you! Your invoice has been paid successfully.
          </Text>
          <Text fontSize="sm" color="gray.600">
            A receipt has been sent to your email address.
          </Text>
        </Center>

        {/* Payment Details (optional static placeholders) */}
        {/* <Stack
          w="100%"
          mx="auto"
          border="1px solid rgba(0,0,0,0.08)"
          p="1.5rem"
          rounded="md"
          mt="2rem"
          gap="0.75rem"
        >
          <Item label="Invoice ID" value="#INV-2025-01" />
          <Item label="Amount Paid" value="$250.00" />
          <Item label="Payment Status" value="Paid" />
          <Item label="Payment Date" value={new Date().toLocaleString()} />
        </Stack> */}

        {/* Action Button */}
        {/* <Center mt="2rem">
          <Link href="/dashboard">
            <Box
              w="16rem"
              h="3rem"
              display="flex"
              alignItems="center"
              justifyContent="center"
              rounded="md"
              border="2px solid"
              borderColor="primary.500"
              bg="primary.500"
              color="white"
              fontWeight={600}
              cursor="pointer"
              _hover={{
                bg: 'primary.600',
                borderColor: 'primary.600',
              }}
              transition="all 0.2s"
            >
              Go to Dashboard
            </Box>
          </Link>
        </Center> */}
      </Box>
    </Grid>
  );
}
