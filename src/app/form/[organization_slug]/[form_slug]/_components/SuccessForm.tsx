'use client';
import React from 'react';
import { FaCircleCheck } from 'react-icons/fa6';
import { Box } from '@chakra-ui/react';

const SuccessForm = () => {
  return (
    <>
      <Box
        width={'100%'}
        height={'100vh'}
        display={'flex'}
        justifyContent={'center'}
        alignItems={'center'}
      >
        <Box
          display={'flex'}
          minW={{ base: '80%', md: '500px' }}
          maxW={{ md: '600px', base: '80%' }}
          height={'40%'}
          mx={'auto'}
          flexDirection={'column'}
          alignItems={'center'}
          justifyContent={'center'}
          gap={5}
          border={'1px solid #e2e8f0'}
          borderRadius={'md'} // Added borderRadius
          boxShadow={'md'} // Removed boxShadow
          p={5}
        >
          <FaCircleCheck size={100} color={'green'} />
          <Box fontSize={'1.5rem'} fontWeight={'bold'} textAlign={'center'}>
            Form Submitted Successfully
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SuccessForm;
