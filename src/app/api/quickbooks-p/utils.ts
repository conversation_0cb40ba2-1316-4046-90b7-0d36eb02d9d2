import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';

export const updateActivities = async (
  booking_id: any,
  status: any,
  supabase: SupabaseClient
) => {
  const { data: activity } = await supabase
    .from(tableNames.client_activities)
    .select('details')
    .eq('details->>booking_id', booking_id)
    .single();

  if (activity) {
    // 2. Merge the new status into existing details
    const updatedDetails = {
      ...activity.details,
      status: status,
    };

    // console.log('updatedDetails', updatedDetails);
    // console.log('activity', activity);

    // 3. Update only the modified details
    await supabase
      .from('client_activities')
      .update({ details: updatedDetails })
      .eq('details->>booking_id', booking_id); //
  }
};
