import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { KlaviyoOAuth } from '@/lib/klaviyo/oauth';
import { KlaviyoDatabase } from '@/lib/klaviyo/db';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
// const KLAVIYO_API_BASE = 'https://a.klaviyo.com/api';
// const CLIENT_ID = process.env.KLAVIYO_CLIENT_ID!;
// const CLIENT_SECRET = process.env.KLAVIYO_CLIENT_SECRET!;

export async function POST(request: Request) {
  const { organization_id } = await request.json();

  if (!organization_id) {
    return NextResponse.json(
      { error: 'Organization ID required' },
      { status: 400 }
    );
  }

  try {
    const { data: klaviyoOrganization, error: fetchError } = await supabaseAdmin
      .from(tableNames.klaviyo_organization)
      .select('*')
      .eq('organization_id', organization_id)
      .single();

    if (fetchError || !klaviyoOrganization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    if (klaviyoOrganization.access_token) {
      // Revoke tokens from Klaviyo
      await KlaviyoOAuth.revokeTokens(klaviyoOrganization.access_token);
      // Delete from database
      await KlaviyoDatabase.deleteIntegration(organization_id, supabaseAdmin);
    }

    return NextResponse.json({ message: 'Klaviyo account disconnected' });
  } catch (error) {
    console.error('Error disconnecting Klaviyo:', error);
    return NextResponse.json(
      { error: 'Failed to disconnect Klaviyo' },
      { status: 500 }
    );
  }
}
