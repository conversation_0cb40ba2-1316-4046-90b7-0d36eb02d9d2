import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { handleStripeConnectCallback } from '@/lib/stripe';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  //console.log('webhook called ', code, state);

  if (error) {
    return NextResponse.redirect('/error?msg=Connection failed');
  }
  if (!code) {
    return NextResponse.redirect(
      new URL('/integrations?error=missing_code', request.url)
    );
  }
  if (!state) {
    return NextResponse.redirect(
      new URL('/integrations?error=missing_state', request.url)
    );
  }
  const user_id = state;
  try {
    const { data: owner } = await supabase
      .from(tableNames.users)
      .select('*')
      .eq('id', Number(user_id))
      .maybeSingle();
    if (!owner) {
      return NextResponse.redirect(
        new URL('/profile?error=invalid_user', request.url)
      );
    }
    await handleStripeConnectCallback(
      code,
      state,
      String(owner.id),
      owner.id,
      supabase
    );
    return NextResponse.redirect(
      new URL('/profile?error=stripe_success', request.url)
    );
  } catch (error) {
    console.error('error from  user ', error);
    return NextResponse.redirect(
      new URL('/profile?error=stripe_error', request.url)
    );
  }
}
