import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export const handlePaymentIntentSucceed = async (
  event: Stripe.PaymentIntent
) => {
  const { invoice_id, client_id, organization_id } = event.metadata;
  // Mark intent as paid

  const { error } = await supabase
    .from(tableNames.payment_links)
    .update({ status: 'paid' })
    .eq('payment_intent_id', String(event.id))
    .eq('invoice_id', Number(invoice_id));
  if (error) {
    return {
      success: false,
      message: `Error updating payment link ${error.message}`,
    };
  }
  // Create Transaction

  const transactionData = {
    reference_id: event.id,
    status: event.status === 'succeeded' ? 'SUCCESS' : event.status,
    amount: Math.floor(Number(event.amount) / 100),
    transaction_type: 'PAYMENT',
    client_id: Number(client_id),
    currency_code: event.currency?.toUpperCase(),
    payment_method: 'CARD', //UPDATE THIS
    organization_id: Number(organization_id),
    stripe_intent_id: event.id,
    invoice_id: Number(invoice_id),
    transaction_date: new Date(event.created * 1000).toISOString(),
  };

  // Check if the transaction already exists to prevent duplicates
  const { data: existingTransaction } = await supabase
    .from(tableNames.transactions)
    .select('id')
    .eq('reference_id', transactionData.reference_id)
    .single();

  if (existingTransaction) {
    //console.log('Transaction already exists:', transactionData.session_id);
    return {
      success: false,
      data: transactionData,
      message: `Transaction already exists: ${transactionData.reference_id}`,
    };
  }

  const { error: terror } = await supabase
    .from(tableNames.transactions)
    .insert(transactionData);
  if (terror) {
    console.error('Error saving transaction:', error);
    return {
      success: false,
      data: transactionData,
      message: `Error saving transaction`,
    };
  }

  // Update invoice

  const { error: ierror } = await supabase
    .from(tableNames.invoices)
    .update({ status: 'PAID' })
    .eq('id', Number(invoice_id));
  if (ierror) {
    console.error('Error updating invoice:', ierror);
    return {
      success: false,
      data: transactionData,
      message: `Error saving transaction`,
    };
  }
  return {
    success: true,
    data: transactionData,
    message: `Operation successful`,
  };
};
