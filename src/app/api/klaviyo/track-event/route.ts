import { env } from '@/constants/env';
import { dispatchKlaviyoEvent } from '@/lib/klaviyo/service';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function POST(request: Request) {
  const body = await request.json();
  const { event, organization_id, action } = body;
  if (event) {
    await dispatchKlaviyoEvent(organization_id, supabase, event, action);
  }
  return NextResponse.json({ message: 'Event created successfully' });
}
