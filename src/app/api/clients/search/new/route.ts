import { createSupabaseServer } from '@/lib/supabase/server';
import { getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';

function cleanSearchString(search: string) {
  if (typeof search !== 'string') return '';
  let cleaned = search.replace(/^\s+/, '');
  cleaned = cleaned.replace(/\s{2,}/g, ' ');
  cleaned = cleaned.replace(/\s+$/, '');
  return cleaned;
}

export async function GET(request: Request) {
  const supabase = createSupabaseServer();

  const { searchParams } = new URL(request.url);

  // Parse query parameters

  const org_id = searchParams.get('org_id') || 1;

  const search = searchParams.get('search_text') || '';
  const page_limit = getNumberParam(searchParams, 'page_limit', 50);
  const page_offset = getNumberParam(searchParams, 'page_offset', 0);

  // Formatted search text
  const formattedSearch = cleanSearchString(search) || '';

  // Supabase RPC call
  const { data, error } = await supabase.rpc('search_clients_simple', {
    search_text: formattedSearch,
    page_offset,
    org_id,
    page_limit,
  });

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}
