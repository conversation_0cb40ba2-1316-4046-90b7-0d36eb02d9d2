import { createSupabaseServer } from '@/lib/supabase/server';
import { getArrayParam, getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';

function cleanSearchString(search: string) {
  if (typeof search !== 'string') return '';
  let cleaned = search.replace(/^\s+/, '');
  cleaned = cleaned.replace(/\s{2,}/g, ' ');
  cleaned = cleaned.replace(/\s+$/, '');
  return cleaned;
}

export async function GET(request: Request) {
  const supabase = createSupabaseServer();
  // const cookieStore = cookies();
  // const userData = cookieStore.get('user_data')?.value;
  // console.log('userData-', userData);
  // const cookiesRes = JSON.parse(userData as any);
  const { searchParams } = new URL(request.url);

  // console.log('search---5', searchParams.get('org_id'));

  // Parse query parameters
  const provinceFilter = getArrayParam(searchParams, 'provinceFilter');
  const leadFilter = getArrayParam(searchParams, 'leadFilter');
  const stageFilter = getArrayParam(searchParams, 'stageFilter');
  const slpFilter = getArrayParam(searchParams, 'slpFilter');
  const goalFilter = getArrayParam(searchParams, 'goal');
  const groupFilter = getArrayParam(searchParams, 'group');
  const activeClients = getArrayParam(searchParams, 'active_clients');
  const consultedBy = getArrayParam(searchParams, 'consulted_by');
  const referralSource = getArrayParam(searchParams, 'referral_source');
  const orgIdFromParams = searchParams.get('org_id'); // Get as string

  const search = searchParams.get('search') || '';
  const currentPage = getNumberParam(searchParams, 'currentPage');
  const size = getNumberParam(searchParams, 'size', 50);

  // Formatted search text
  const formattedSearch = cleanSearchString(search) || '';
  const page_limit = size;

  const getActiveClients = () => {
    if (
      activeClients.length === 0 ||
      (activeClients.includes('true') && activeClients.includes('false'))
    ) {
      return null;
    }
    return activeClients[0] === 'true';
  };

  // Determine organization ID with priority:
  // 1. Explicit org_id from params (as string)
  // 2. Cookie organization_id
  // 3. Default to "1" (as string)
  // const organizationId =
  //   orgIdFromParams || cookiesRes?.organization_id?.toString() || '1';

  // console.log('cookiesRes', cookiesRes?.organization_id);
  // console.log('organizationId', organizationId);

  // Supabase RPC call
  const { data, error } = await supabase.rpc('search_real', {
    province_filter: provinceFilter.length ? provinceFilter : null,
    lead_quality_filter: leadFilter.length ? leadFilter : null,
    stage_filter: stageFilter.length ? stageFilter : null,
    slp_filter: slpFilter.length ? slpFilter : null,
    goals_filter: goalFilter.length ? goalFilter : null,
    group_filter: groupFilter.length ? groupFilter : null,
    search_text: formattedSearch,
    active_client: getActiveClients(),
    organization_filter: Number(orgIdFromParams), // Passed as string
    page_offset: (currentPage - 1) * page_limit,
    page_limit,
    consulted_by: consultedBy.length ? consultedBy : null,
    referral_source: referralSource.length ? referralSource : null,
  });

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}
