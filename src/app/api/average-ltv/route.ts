import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServer } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  const supabase = createSupabaseServer();

  const { searchParams } = new URL(request.url);
  const orgId = searchParams.get('organization_id');
  const slpId = searchParams.get('slp_id');

  if (!orgId) {
    return NextResponse.json(
      { message: 'organization_id is required' },
      { status: 400 }
    );
  }

  if (!slpId) {
    return NextResponse.json(
      { message: 'slp_id is required' },
      { status: 400 }
    );
  }

  const organization_id = Number(orgId);
  const slp_id = Number(slpId);

  //Fetch LTV stats using RPC (already SLP-specific)
  const { data: invoiceStats, error: invoiceStatsError } = await supabase.rpc(
    'get_ltv_stats_by_slp',
    { slp_id_input: slp_id, org_id_input: organization_id }
  );

  if (invoiceStatsError) {
    return NextResponse.json(
      { message: invoiceStatsError.message },
      { status: 500 }
    );
  }

  const {
    paying_clients = 0,
    average_invoice_count = 0,
    total_revenue = 0,
    average_ltv = 0,
  } = invoiceStats?.[0] || {};

  return NextResponse.json(
    {
      average_ltv: Number(average_ltv),
      average_invoice_count: Number(average_invoice_count),
      paying_clients: Number(paying_clients),
      total_revenue: Number(total_revenue),
    },
    { status: 200 }
  );
}
