import { createInvoiceItems } from '@/app/service/invoice_items';
import { getPackageById } from '@/app/service/package';
import { createPurchase } from '@/app/service/purchase';
import { createService, getServiceById } from '@/app/service/service';
import { getTaxById } from '@/app/service/taxes';
import { tableNames } from '@/constants/table_names';
import { formatNumber } from '@/utils/num-format';
import { SupabaseClient } from '@supabase/supabase-js';

export const getTaxValueForAnItem = async (item: any, supabase: any) => {
  let totalTax = 0;

  const { price, taxes, quantity } = item;
  // console.log('item is ', item);

  const itemTaxIds = taxes.map((tax: any) => Number(tax.id));
  const allTaxes = await getTaxById([...new Set(itemTaxIds)], supabase);

  if (Array.isArray(itemTaxIds) && itemTaxIds.length > 0) {
    const itemTaxes = allTaxes.filter((tax: any) =>
      itemTaxIds.includes(tax.id)
    );
    // Verify all taxes were found
    if (itemTaxes.length !== itemTaxIds.length) {
      const foundIds = itemTaxes.map((t: any) => t.id);
      const missingIds = itemTaxIds.filter((id) => !foundIds.includes(id));
      throw new Error(`Taxes not found: ${missingIds.join(', ')}`);
    }

    // Calculate total tax amount
    const totalTaxAmount = itemTaxes.reduce((sum: any, tax: any) => {
      const taxValue = Number(tax.value);
      return sum + (taxValue / 100) * Number(price * quantity);
    }, 0);

    totalTax += totalTaxAmount;
  }
  return totalTax;
};
export const handleAllTaxPlusPrice = async (
  items: any,
  supabase: SupabaseClient
) => {
  let totalPricePlusTax = 0;

  for (const item of items) {
    const { price, taxes, quantity = 1 } = item;
    const itemTaxIds = taxes.map((tax: any) => Number(tax.id));
    const allTaxes = await getTaxById([...new Set(itemTaxIds)], supabase);

    if (Array.isArray(itemTaxIds) && itemTaxIds.length > 0) {
      const itemTaxes = allTaxes.filter((tax: any) =>
        itemTaxIds.includes(tax.id)
      );
      // Verify all taxes were found
      if (itemTaxes.length !== itemTaxIds.length) {
        const foundIds = itemTaxes.map((t: any) => t.id);
        const missingIds = itemTaxIds.filter((id) => !foundIds.includes(id));
        throw new Error(`Taxes not found: ${missingIds.join(', ')}`);
      }

      // Calculate total tax amount
      const totalTaxAmount = itemTaxes.reduce((sum: any, tax: any) => {
        const taxValue = Number(tax.value);
        return sum + (taxValue / 100) * Number(price * quantity);
      }, 0);

      totalPricePlusTax += Number(price * quantity) + totalTaxAmount;
    } else {
      totalPricePlusTax += Number(price * quantity);
    }
  }

  return totalPricePlusTax;
};
export const handleInvoiceItems = async (
  invoiceId: any,
  items: any[],
  supabase: SupabaseClient,
  restPayload?: any
) => {
  if (!invoiceId) throw new Error('Invoice ID is required.');
  if (!Array.isArray(items) || items.length === 0) {
    throw new Error('At least one invoice item is required.');
  }

  const createdItems: any[] = [];
  let allTaxIds: number[] = [];

  // First pass: collect all tax IDs to fetch them in one go
  items.forEach((item) => {
    if (item.tax_ids && Array.isArray(item.tax_ids)) {
      allTaxIds = [...allTaxIds, ...item.tax_ids.map((id: any) => Number(id))];
    }
  });

  // Fetch all unique taxes at once
  const uniqueTaxIds = [...new Set(allTaxIds)];
  const allTaxes = await getTaxById(uniqueTaxIds, supabase);

  for (const item of items) {
    const {
      package_id,
      service_id,
      tax_ids,
      price: basePrice,
      quantity,
      product_name,
      new_item,
      description,
      duration_minutes,
    } = item;
    if (!service_id && !package_id && !new_item) {
      throw new Error('At least one invoice product is required.');
    }
    const actualPrice = Number(basePrice) * (Number(quantity) || 1);

    const payload: Record<string, any> = {
      quantity,
      product_name,
      description,
      invoice_id: invoiceId,
      price: basePrice, // Start with actual price price
    };
    if (new_item) {
      const { user_id, organization_id } = restPayload;
      const service = await createService(
        {
          organization_id,
          created_by: user_id,
          name: product_name,
          description,
          price: Number(basePrice),
          duration_minutes,
          tax_ids,
        },
        supabase
      );
      payload.service_id = service?.id;
    }

    // Handle package or service
    if (item?.package_id) {
      const pkg = await getPackageById(Number(package_id), supabase);
      if (!pkg) throw new Error(`Package not found: ${package_id}`);
      payload.package_id = package_id;
    } else {
      const service = await getServiceById(
        Number(new_item ? payload?.service_id : service_id),
        supabase
      );
      if (!service)
        throw new Error(
          `Service not found: ${new_item ? payload?.service_id : service_id}`
        );
      payload.service_id = new_item ? payload?.service_id : service_id;
    }

    // Handle taxes
    if (tax_ids && Array.isArray(tax_ids) && tax_ids.length > 0) {
      const numericTaxIds = tax_ids.map((id: any) => Number(id));
      const itemTaxes = allTaxes.filter((tax: any) =>
        numericTaxIds.includes(tax.id)
      );

      // Verify all taxes were found
      if (itemTaxes.length !== numericTaxIds.length) {
        const foundIds = itemTaxes.map((t: any) => t.id);
        const missingIds = numericTaxIds.filter((id) => !foundIds.includes(id));
        throw new Error(`Taxes not found: ${missingIds.join(', ')}`);
      }

      // Calculate total tax amount
      const totalTaxAmount = itemTaxes.reduce((sum: any, tax: any) => {
        const taxValue = Number(tax.value);
        return sum + (taxValue / 100) * Number(actualPrice);
      }, 0);

      // console.log('totalTaxAmount is ', totalTaxAmount);
      // console.log('price', price);
      // console.log('cal', Number(price) + totalTaxAmount);

      payload.price = Number(basePrice);
      payload.tax_value = formatNumber(totalTaxAmount);
      payload.tax_ids = tax_ids;
    }

    const created_item = await createInvoiceItems(payload, supabase);
    // Create the purchased items
    await createPurchasedItems(
      { ...created_item, ...restPayload, invoiceId },
      supabase
    );
    createdItems.push(created_item);
  }

  return createdItems.reduce(
    (sum: number, item: any) => sum + Number(item.price * item?.quantity),
    0
  );
};

export const createPurchasedItems = async (item: any, supabase: any) => {
  if (!item?.id) throw new Error('Invalid item');
  const {
    id,
    user_id,
    organization_id,
    client_id,
    service_id,
    package_id,
    quantity,
    invoiceId: invoice_id,
    invoice_date,
    purchaseStatus = 'UNREDEEMED',
    booking_id,
  } = item;
  const is_package = item?.package_id;

  if (is_package) {
    const pkg = await getPackageById(Number(package_id), supabase);
    if (!pkg) throw new Error(`Package not found: ${package_id}`);

    // 1. Load package ITEMS PART
    const { data: items, error: itemsErr } = await supabase
      .from(tableNames.package_items)
      .select('id, service_id, quantity')
      .eq('package_offering_id', item?.package_id);
    if (itemsErr) throw itemsErr;
    if (!items || items.length === 0) {
      throw new Error('No package items found for this offering');
    }
    const toInsert: Array<any> = [];
    for (const item of items) {
      for (let i = 0; i < item.quantity * quantity; i++) {
        toInsert.push({
          client_id,
          organization_id,
          user_id,
          invoice_item_id: id,
          service_id: item?.service_id,
          invoice_id,
          created_at: invoice_date ? invoice_date : new Date(),
          status: purchaseStatus,
          booking_id,
        });
      }
    }
    const createdPurchase = await createPurchase(toInsert, supabase);
    return createdPurchase;
  } else {
    const service = await getServiceById(Number(service_id), supabase);
    if (!service) throw new Error(`Service not found : ${service_id}`);

    const toInsert: Array<any> = [];
    // for (const item of items) {
    for (let i = 0; i < quantity; i++) {
      toInsert.push({
        client_id,
        organization_id,
        user_id,
        invoice_item_id: id,
        service_id: item?.service_id,
        invoice_id,
        created_at: invoice_date ? invoice_date : new Date(),
        status: purchaseStatus,
        booking_id,
      });
    }
    // }
    const createdPurchase = await createPurchase(toInsert, supabase);
    return createdPurchase;
  }
};
