import { updatePurchaseById } from '@/app/service/purchase';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServer();
    const { id } = params;
    const body = await request.json();
    const updatedPurchase = await updatePurchaseById(id, body, supabase);

    return NextResponse.json({
      success: true,
      data: updatedPurchase,
    });
  } catch (error: any) {
    console.error('Unexpected error in PUT /api/purchase/[id]:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
        }),
      },
      { status: 500 }
    );
  }
}
