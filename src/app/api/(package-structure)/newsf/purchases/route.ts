import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const supabase = createSupabaseServer();

  const org_id = searchParams.get('organization_id');

  const currentPage = getNumberParam(searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  // Optional filters
  const client_id = searchParams.get('client_id');
  const status = searchParams.get('status');
  const slp_id = searchParams.get('slp_id');

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;

  let query = supabase.from(tableNames.services_purchases).select(
    `
       *, service:services(*)
        `,
    { count: 'exact' }
  );

  if (client_id) {
    query = query.eq('client_id', Number(client_id));
  }
  if (org_id) {
    query = query.eq('organization_id', Number(org_id));
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (slp_id) {
    query = query.eq('slp_id', Number(slp_id));
  }

  // Execute query with pagination and ordering
  const {
    data: purchases,
    error: queryError,
    count,
  } = await query
    .order('created_at', { ascending: false })
    .range(startIndex, endIndex);

  if (queryError) {
    console.error('Supabase query error:', queryError);
    return NextResponse.json(
      {
        error: 'Failed to fetch purchases',
        code: queryError.code,
      },
      { status: 500 }
    );
  }

  // Handle empty results
  if (!purchases) {
    return NextResponse.json({
      data: [],
      pagination: {
        page_number: currentPage,
        total_count: 0,
        items_per_page: itemsPerPage,
      },
    });
  }

  return NextResponse.json({
    data: purchases,
    pagination: {
      page_number: currentPage,
      total_count: count || 0,
      items_per_page: itemsPerPage,
    },
  });
}
