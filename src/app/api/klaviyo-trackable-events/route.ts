import { KlaviyoActions } from '@/constants/klaviyo-actions';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

// Updated API route handler
export async function GET(request: any) {
  try {
    const supabase = createSupabaseServer();

    const { searchParams } = new URL(request.url);

    const organization_id = searchParams.get('organization_id') || '';

    if (!organization_id) {
      return NextResponse.json(
        { success: false, message: 'Organization is required.' },
        { status: 500 }
      );
    }
    const { data, error } = await supabase
      .from(tableNames.klaviyo_trackable_events)
      .select('*')
      .eq('organization_id', Number(organization_id));

    if (error) throw error;
    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    console.error(error);
    return NextResponse.json(
      { success: false, message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: any) {
  try {
    const supabase = createSupabaseServer();
    const payload = await request.json();
    const { searchParams } = new URL(request.url);
    const organization_id = searchParams.get('organization_id') || '';

    if (!organization_id) {
      return NextResponse.json(
        { success: false, message: 'Organization is required.' },
        { status: 400 }
      );
    }

    const allActions = Object.values(KlaviyoActions);
    const { data: existingEvents } = await supabase
      .from(tableNames.klaviyo_trackable_events)
      .select('*')
      .eq('organization_id', Number(organization_id));

    // Process payload items
    const updates = [];
    const creates = [];

    // Handle items in payload
    for (const eventName of payload) {
      if (!allActions.includes(eventName)) {
        continue; // Skip invalid events
      }

      const existingEvent = existingEvents?.find(
        (e) => e.event_name === eventName
      );

      if (existingEvent) {
        if (!existingEvent.is_enabled) {
          // Enable existing but disabled event
          updates.push({
            id: existingEvent.id,
            event_name: eventName,
            organization_id: Number(organization_id),
            is_enabled: true,
          });
        }
      } else {
        // Create new event
        creates.push({
          event_name: eventName,
          organization_id: Number(organization_id),
          is_enabled: true,
        });
      }
    }

    // Disable existing events that are not in payload
    for (const existingEvent of existingEvents || []) {
      if (
        !payload.includes(existingEvent.event_name) &&
        existingEvent.is_enabled
      ) {
        updates.push({
          id: existingEvent.id,
          event_name: existingEvent.event_name,
          organization_id: Number(organization_id),
          is_enabled: false,
        });
      }
    }

    // Perform updates
    if (updates.length > 0) {
      const { error: updateError } = await supabase
        .from(tableNames.klaviyo_trackable_events)
        .upsert(updates);
      if (updateError) throw updateError;
    }

    // Perform creates
    if (creates.length > 0) {
      const { error: createError } = await supabase
        .from(tableNames.klaviyo_trackable_events)
        .insert(creates);
      if (createError) throw createError;
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(error);
    return NextResponse.json(
      { success: false, message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
