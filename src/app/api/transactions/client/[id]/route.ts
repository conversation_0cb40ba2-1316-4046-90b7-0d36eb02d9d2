import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Input validation
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Transaction ID is required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseServer();

    // Fetch client's Transaction that have not been linked to an invoice
    const { data: transactions, error: queryError } = await supabase
      .from(tableNames.transactions)
      .select('*')
      .eq('client_id', Number(id))
      .is('invoice_id', null);

    if (queryError) {
      console.error('Supabase query error:', queryError);

      return NextResponse.json(
        {
          error: 'Failed to fetch Transaction',
          code: queryError.code,
          message: queryError.message,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: transactions,
    });
  } catch (error: any) {
    console.error(
      'Unexpected error in GET /api/transactions/client/[id]:',
      error
    );

    return NextResponse.json(
      {
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
        }),
      },
      { status: 500 }
    );
  }
}
