import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { createPurchasedItems } from '../../(package-structure)/newsf/invoice/util';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const organization_id = body?.organization_id;
    const createdPurchases = [];
    //1 get all invoices plus their items
    const { data: invoices, error } = await supabaseAdmin
      .from(tableNames.invoices)
      .select(
        `id,
         invoice_date,
         slp_id,
         organization_id,
         client_id, 
        invoice_items(*)`
      )
      .eq('organization_id', organization_id);
    if (error) throw error;
    for (const invoice of invoices) {
      if (invoice?.invoice_items?.length > 0) {
        for (const item of invoice.invoice_items) {
          const payload = {
            id: item.id,
            user_id: invoice.slp_id,
            organization_id: invoice?.organization_id,
            client_id: invoice?.client_id,
            service_id: item.service_id,
            package_id: item.package_id,
            invoiceId: invoice.id,
            invoice_date: invoice.invoice_date,
          };
          //console.log(' payload is ', payload);

          const res = await createPurchasedItems(payload, supabaseAdmin);
          createdPurchases.push(res);
        }
      }
    }
    return NextResponse.json(createdPurchases, { status: 200 });
  } catch (error: any) {
    console.error('Error in POST request:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
