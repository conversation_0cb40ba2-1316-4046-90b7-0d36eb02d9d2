import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServer } from '@/lib/supabase/server';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { backgroundSync } from '../gmail/utils';

const clientId = process.env.GMAIL_CLIENT_ID as string;
const clientSecret = process.env.GMAIL_SECRET_KEY as string;
const redirectUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`;

const oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUrl);

export async function POST(req: NextRequest) {
  const supabase = createSupabaseServer();

  try {
    const { userEmail, uniqueMessageIds, query, tokens } = await req.json();

    console.log(
      `Starting background sync for ${userEmail} - ${uniqueMessageIds.length} emails`
    );

    //Re-authenticate using tokens
    oauth2Client.setCredentials({
      access_token: tokens.google_access_token,
      refresh_token: tokens.google_refresh_token,
    });

    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    await backgroundSync(supabase, gmail, uniqueMessageIds, query, tokens);

    console.log(`Background sync completed for ${userEmail}`);

    return NextResponse.json({
      success: true,
      message: 'Background sync completed',
      processedEmails: uniqueMessageIds.length,
    });
  } catch (error: any) {
    console.error('Background sync error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error?.message || 'Background sync failed',
      },
      { status: 200 } // prevent retries
    );
  }
}
