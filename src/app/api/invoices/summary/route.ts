import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const supabase = createSupabaseServer();
    const { searchParams } = new URL(request.url);

    const organization_id = searchParams.get('organization_id');
    const slp_id = searchParams.get('slp_id');

    if (!organization_id) {
      return NextResponse.json(
        { error: 'organization_id is required' },
        { status: 400 }
      );
    }

    // Try to query with due_date first, fallback if column doesn't exist
    let invoices: any[] = [];
    let hasDueDateColumn = true;

    try {
      // First attempt: try to select with due_date column
      let query = supabase
        .from(tableNames.invoices)
        .select('status, total_price, created_dt, invoice_date, due_date')
        .eq('organization_id', organization_id)
        .or('status.neq.DELETED,status.is.null'); // Exclude deleted invoices at DB level

      // Add slp_id filter if provided
      if (slp_id) {
        query = query.eq('slp_id', slp_id);
      }

      const { data, error } = await query;

      if (error && error.message.includes('due_date')) {
        // Column doesn't exist, set flag and continue with fallback
        hasDueDateColumn = false;
      } else if (error) {
        throw error;
      } else {
        invoices = data || [];
      }
    } catch (error: any) {
      if (error.message.includes('due_date')) {
        hasDueDateColumn = false;
      } else {
        throw error;
      }
    }

    // Fallback query without due_date if column doesn't exist
    if (!hasDueDateColumn) {
      let query = supabase
        .from(tableNames.invoices)
        .select('status, total_price, created_dt, invoice_date')
        .eq('organization_id', organization_id)
        .or('status.neq.DELETED,status.is.null');

      if (slp_id) {
        query = query.eq('slp_id', slp_id);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching invoices:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      invoices = data || [];
    }

    // Calculate summary data using optimized logic
    const now = new Date();
    const nowTime = now.getTime();
    const thirtyDaysFromNowTime = nowTime + 30 * 24 * 60 * 60 * 1000;
    const thirtyDaysAgoTime = nowTime - 30 * 24 * 60 * 60 * 1000;

    // Initialize counters
    let overdueCount = 0;
    let overdueTotal = 0;
    let dueWithin30Count = 0;
    let dueWithin30Total = 0;
    let recentCount = 0;
    let recentTotal = 0;

    // Process invoices in a single loop for better performance
    invoices.forEach((invoice) => {
      const totalPrice = invoice.total_price || 0;

      // Check if invoice is active (not void/deleted/paid/completed)
      const isActive =
        invoice.status &&
        !['VOID', 'DELETED', 'PAID', 'COMPLETED'].includes(
          invoice.status.toUpperCase()
        );

      // Calculate overdue and due within 30 days only if due_date column exists and invoice is active
      if (hasDueDateColumn && isActive && invoice.due_date) {
        const dueDate = new Date(invoice.due_date);
        const dueDateTime = dueDate.getTime();

        if (dueDateTime < nowTime) {
          // Overdue
          overdueCount++;
          overdueTotal += totalPrice;
        } else if (
          dueDateTime > nowTime &&
          dueDateTime < thirtyDaysFromNowTime
        ) {
          // Due within 30 days
          dueWithin30Count++;
          dueWithin30Total += totalPrice;
        }
      }

      // Calculate recent invoices (created in last 30 days) regardless if it's paid or not
      const createdDate = new Date(invoice.created_dt || invoice.invoice_date);
      if (createdDate.getTime() > thirtyDaysAgoTime) {
        recentCount++;
        recentTotal += totalPrice;
      }
    });

    const summaryData = {
      // This is invoices that was created and is overdue which means it's past due_date and has not been paid
      overdue: {
        count: overdueCount,
        total: overdueTotal,
      },
      // This is invoices that will be due from now till 30 days
      dueWithin30: {
        count: dueWithin30Count,
        total: dueWithin30Total,
      },
      // This is invoices that were created in last 30 days regardless if it's paid or not
      recent: {
        count: recentCount,
        total: recentTotal,
      },
      // Status counts for badges
      statusCounts: {
        unpaid: invoices.filter(
          (invoice) =>
            invoice.status &&
            ['PARTIALLY_PAID', 'AWAITING_PAYMENT'].includes(
              invoice.status.toUpperCase()
            )
        ).length,
        total: invoices.length,
      },
    };

    return NextResponse.json(summaryData);
  } catch (error: any) {
    console.error('Error in invoice summary API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
