import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: Request,
  { params }: { params: { email: string } }
) {
  try {
    const { email } = params;
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');

    //console.log('organizationId', organizationId);
    if (!email) {
      return NextResponse.json(
        { message: 'Username is required' },
        { status: 400 }
      );
    }
    const { data } = await supabaseAdmin
      .from(tableNames.client_emails)
      .select('*,client:clients!client_emails_client_id_fkey(*)')
      .eq('email', email)
      .eq('organization_id', Number(organizationId));
    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
