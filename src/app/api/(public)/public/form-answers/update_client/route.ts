// API Route Handler (PUT /api/public/form-answers/update_client)
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function PUT(request: NextRequest) {
  try {
    const { clientId, updateData } = await request.json();

    if (!clientId) {
      return NextResponse.json(
        { message: 'Client ID is required' },
        { status: 400 }
      );
    }

    // First, get the existing record to merge data if needed
    const { data: existingRecord, error: fetchError } = await supabaseAdmin
      .from(tableNames.clients)
      .select('*') // Added missing select method
      .eq('id', clientId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { message: fetchError.message },
        { status: 500 }
      );
    }

    // Merge existing data with new data
    const mergedData = {
      ...existingRecord,
      ...updateData,
      updated_at: new Date().toISOString(),
    };

    // Update the record
    const { data, error: updateError } = await supabaseAdmin
      .from(tableNames.clients)
      .update(mergedData)
      .eq('id', clientId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { message: updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error updating client:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
