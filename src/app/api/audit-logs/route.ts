import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { getNumberParam } from '@/utils/format-object';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function POST(request: NextRequest) {
  const body = await request.json();

  const { data, error } = await supabaseAdmin
    .from(tableNames.audit_logs)
    .insert(body)
    .select();

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json(data?.[0], { status: 200 });
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  const org_id = searchParams.get('organization_id');

  const currentPage = getNumberParam(searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  // Optional filters
  const client_id = searchParams.get('client_id');
  const status = searchParams.get('status');
  const slp_id = searchParams.get('slp_id');

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;

  let query = supabaseAdmin
    .from(tableNames.audit_logs)
    .select(`*,users(*)`, { count: 'exact' });

  if (client_id) {
    query = query.eq('client_id', Number(client_id));
  }
  if (org_id) {
    query = query.eq('organization_id', Number(org_id));
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (slp_id) {
    query = query.eq('slp_id', Number(slp_id));
  }

  // Execute query with pagination and ordering
  const {
    data: auditLogs,
    error: queryError,
    count,
  } = await query
    .order('created_at', { ascending: false })
    .range(startIndex, endIndex);

  if (queryError) {
    console.error('Supabase query error:', queryError);
    return NextResponse.json(
      {
        error: 'Failed to fetch purchases',
        code: queryError.code,
      },
      { status: 500 }
    );
  }

  // Handle empty results
  if (!auditLogs) {
    return NextResponse.json({
      data: [],
      pagination: {
        page_number: currentPage,
        total_count: 0,
        items_per_page: itemsPerPage,
      },
    });
  }

  return NextResponse.json({
    data: auditLogs,
    pagination: {
      page_number: currentPage,
      total_count: count || 0,
      items_per_page: itemsPerPage,
    },
  });
}
