/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';

export const findAllSlpNotesByClient = async (
  client_id: any,
  supabase: any
) => {
  let offset = 0;
  let allData: any = [];
  const fetchLimit = 1000;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.slp_notes)
      .select('*')
      .eq('client_id', Number(client_id))
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;
    if (data.length === 0) {
      break;
    }

    allData = allData.concat(data);

    offset += fetchLimit;
  }

  return allData;
};
