import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';

export const getKlaviyoOrganizationById = async (
  organization_id: any,
  supabase: SupabaseClient
) => {
  const { data, error } = await supabase
    .from(tableNames.klaviyo_organization)
    .select('*')
    .eq('organization_id', Number(organization_id))
    .maybeSingle();
  if (error) throw error;
  return data;
};
