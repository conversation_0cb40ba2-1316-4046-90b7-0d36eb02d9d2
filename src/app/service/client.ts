import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';

export const updateClientById = async (
  id: any,
  data: any,
  supabase: SupabaseClient
) => {
  const { data: InvoiceData, error } = await supabase
    .from(tableNames.clients)
    .update(data)
    .eq('id', Number(id))
    .select('*')
    .maybeSingle();
  if (error) throw error;
  return InvoiceData;
};
