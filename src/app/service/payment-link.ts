import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';

export const getPaymentLinkDetails = async (
  id: any,
  supabase: SupabaseClient
) => {
  const { data: paymentLink, error } = await supabase
    .from(tableNames.payment_links)
    .select('*, invoices:invoice_id(*)')
    .eq('id', id)
    .eq('status', 'pending')
    .gt('expires_at', new Date().toISOString())
    .single();
  if (error) throw error;
  return paymentLink;
};
