import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';

export const createPurchase = async (data: any, supabase: SupabaseClient) => {
  const { data: Purchase, error } = await supabase
    .from(tableNames.services_purchases)
    .insert(data)
    .select('*');
  if (error) throw error;
  return Purchase;
};

export const updatePurchaseById = async (
  id: any,
  data: any,
  supabase: SupabaseClient
) => {
  const { data: PurchaseData, error } = await supabase
    .from(tableNames.services_purchases)
    .update(data)
    .eq('id', Number(id))
    .select('*')
    .maybeSingle();
  if (error) throw error;
  return PurchaseData;
};
