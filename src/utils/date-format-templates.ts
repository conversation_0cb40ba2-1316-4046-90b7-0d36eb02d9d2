import moment from 'moment';

/**
 * Array of moment.js date format strings
 * Based on common international date formatting patterns
 */
export const dateFormats: string[] = [
  // Day-First formats (European style)
  'DD/M/YY', // 20/8/25
  'DD-MM-YYYY', // 20-08-2025
  'Do MMMM, YYYY', // 20th August, 2025

  // Month-First formats (US style)
  'MM/DD/YYYY', // 08/20/2025
  'MM-DD-YYYY', // 08-20-2025
  'MMM DD, YYYY', // Aug 20, 2025
  'MMMM DD, YYYY', // August 20, 2025

  // Year-First formats (ISO style)
  'YYYY/MM/DD', // 2025/08/20
  'YYYY-MM-DD', // 2025-08-20
  'YYYY MMM DD', // 2025 Aug 20
  'YYYY MMMM DD', // 2025 August 20
];

/**
 * Format a date using a specific moment.js format string
 */
export function formatDateWithFormat(
  date: Date | string | moment.Moment,
  formatString: string
): string {
  if (!date) return '';

  const momentDate = moment(date);
  if (!momentDate.isValid()) return '';

  try {
    return momentDate.format(formatString);
  } catch (error) {
    console.warn('Invalid date format:', formatString, error);
    return momentDate.format('MMM D, YYYY'); // fallback
  }
}

/**
 * Get all available format examples for current date
 */
export function getAllFormatExamples(): Array<{
  format: string;
  formatted: string;
}> {
  const currentDate = new Date();

  return dateFormats.map((format) => ({
    format,
    formatted: formatDateWithFormat(currentDate, format),
  }));
}
