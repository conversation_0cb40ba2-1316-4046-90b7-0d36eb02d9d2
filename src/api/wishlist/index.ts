import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getAllWaitlists() {
  const baseUrl = '/api/waitlists';
  const response = await fetch(baseUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch waitlists');
  }
  const data = await response.json();
  return data;
}

export const useGetAllWaitlists = (
  config?: QueryConfigType<typeof getAllWaitlists>
) => {
  return useQuery<ExtractFnReturnType<typeof getAllWaitlists>>({
    queryKey: [queryKey.overview.getAllWaitlists],
    queryFn: () => getAllWaitlists(),
    ...config,
  });
};
