import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export type TFilter = {
  items_per_page: number;
  current_page: number;
  event: string;
  date: string;
};

async function notableDateView(
  client_id?: number,
  filter?: TFilter | undefined
) {
  const baseUrl = `/api/notable-dates`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    client_id: client_id,
    ...(filter ? filter : {}),
  });
  const response = await fetch(apiUrl, { method: 'GET' });
  const json = await response.json();
  return json;
}

type QueryFnType = typeof notableDateView;
export const useNotableDateViewQuery = (
  client_id?: number,
  filter?: TFilter | undefined,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.notableDates?.getAll, client_id, filter],
    queryFn: () => notableDateView(client_id, filter),
    ...config,
  });
};
