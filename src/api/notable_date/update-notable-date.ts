import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const updateNotableDate = async (data: { body: any; id: any }) => {
  const response = await fetch(`/api/notable-dates/${data?.id}`, {
    method: 'PATCH',
    body: JSON.stringify(data?.body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating date');
  }
  return response.json();
};

type QueryFnType = typeof updateNotableDate;

export const useUpdateNotableDateAPI = (
  config?: MutationConfig<QueryFnType>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Note data updated',
      });
      queryClient.invalidateQueries([queryKey.notableDates.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notableDates.update],
    mutationFn: updateNotableDate,
    ...config,
  });
};
