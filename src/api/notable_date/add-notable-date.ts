import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';

const addNotableDate = async (body: any) => {
  const response = await fetch(`/api/notable-dates`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating note');
  }
  return response.json();
};

type QueryFnType = typeof addNotableDate;

export const useAddNotableDateApi = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: ToastMessages.somethingWrong || err?.message,
      });
    },
    onSuccess: () => {
      // toaster.create({
      //   type: 'success',
      //   description: 'New notable date created',
      // });
      queryClient.invalidateQueries([queryKey.notableDates?.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notableDates?.create],
    mutationFn: addNotableDate,
    ...config,
  });
};
