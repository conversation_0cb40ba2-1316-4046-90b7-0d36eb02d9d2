import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const deleteNotableDate = async (id: any) => {
  const response = await fetch(`/api/notable-dates/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error deleting note');
  }
  return response.json();
};

type QueryFnType = typeof deleteNotableDate;

export const useDeleteNotableDateApi = (
  config?: MutationConfig<QueryFnType>
) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Date deleted successfully',
      });
      queryClient.invalidateQueries([queryKey.notableDates.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notableDates.delete],
    mutationFn: deleteNotableDate,
    ...config,
  });
};
