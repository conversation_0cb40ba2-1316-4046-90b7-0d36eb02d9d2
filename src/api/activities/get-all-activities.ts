//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
export async function getAllActivities(id: number) {
  const response = await fetch(`/api/clients/${id}/activities`);
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
}
type QueryFnType = typeof getAllActivities;

type options = QueryConfigType<QueryFnType>;

export const useGetClientActivitiesQuery = (id: number, config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.client.getActivities, id],
    queryFn: () => getAllActivities(id),
    ...config,
  });
};
