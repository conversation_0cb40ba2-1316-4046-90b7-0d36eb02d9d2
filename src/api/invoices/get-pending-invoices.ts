//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getPendingInvoices(linkedSlp: boolean) {
  if (typeof window === 'undefined') return [];

  const userState = JSON.parse(localStorage.getItem('UserState') || '{}');
  const orgId = userState?.UserState?.organization?.id;

  if (!orgId) throw new Error('Organization ID not found');
  const response = await fetch(
    `/api/invoices/pending?linkedSlp=${linkedSlp}&org=${orgId}`
  );
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Unable to fetch invoices');
  }

  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getPendingInvoices;

export const useGetPendingInvoicesApi = (
  linkedSlp: boolean,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getPending, linkedSlp],
    queryFn: () => getPendingInvoices(linkedSlp),
    ...config,
  });
};
