import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const createAndSendInvoice = async (body: any) => {
  const response = await fetch(`/api/quickbooks-p`, {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw {
      message: errorData.message || 'Error creating invoice',
      status: response.status,
    };
  }

  return response.json();
};

type QueryFnType = typeof createAndSendInvoice;

export const useCreateAndSendInvoiceMutation = (
  config?: MutationConfig<QueryFnType> & { disableToast?: boolean }
) => {
  return useMutation({
    mutationKey: [queryKey.invoices.createAndSendInvoice],
    mutationFn: createAndSendInvoice,
    onError: (err: { message: string; status: number }) => {
      if (!config?.disableToast) {
        const errorMessage =
          err?.status === 400
            ? err.message
            : err?.message || ToastMessages.somethingWrong;

        toaster.create({
          type: 'error',
          description: errorMessage,
        });
      }
    },
    onSuccess: () => {
      if (!config?.disableToast) {
        toaster.create({
          type: 'success',
          description: ToastMessages.invoices.createSuccess,
        });
      }
    },
    retry: false,
    ...config,
  });
};
