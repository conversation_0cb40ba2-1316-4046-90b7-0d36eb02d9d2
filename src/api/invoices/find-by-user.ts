//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
export type TFilter = {
  limit?: number;
  page?: number;
  client_id?: number;
  org_id?: number;
  status?: string;
  date_to?: string;
  date_from?: string;
  invoice_no?: number;
};

async function findByUser(data: {
  id: number;
  // limit?: number;
  // org_id: number;
  filter?: TFilter;
}) {
  const baseUrl = `/api/invoices/user/${data.id}`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    // limit: data.limit,
    // org_id: data.org_id,
    ...(data.filter ? data.filter : {}),
  });
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }

  const res = await response.json();
  return res;
}

type QueryFnType = typeof findByUser;
type options = QueryConfigType<QueryFnType>;

export const useGetInvoicesByUserQuery = (
  data: { id: number; filter: TFilter },
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getByUser, data],
    queryFn: () => findByUser(data),
    ...config,
  });
};
