//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { IGetDoneInvoicesFilter } from '@/store/filters/invoices';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { getAllInvoices } from './find-all';
import { queryKey } from '@/constants/query-key';

async function getDoneInvoices(filter: IGetDoneInvoicesFilter) {
  const baseUrl = '/api/invoices/get-done';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllInvoices;
type options = QueryConfigType<QueryFnType>;
export const useGetDoneInvoicesQuery = (
  filter: IGetDoneInvoicesFilter,
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getDoneInvoices, filter],
    queryFn: () => getDoneInvoices(filter),
    ...config,
  });
};
