//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const updateInvoice = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/invoices/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ ...body.data }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating invoices data');
  }
  return response.json();
};

type QueryFnType = typeof updateInvoice;

export const useUpdateInvoiceMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toaster.create({
    //     type: 'success',
    //     description: ToastMessages.invoices.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.invoices.update],
    mutationFn: updateInvoice,
    ...config,
  });
};
