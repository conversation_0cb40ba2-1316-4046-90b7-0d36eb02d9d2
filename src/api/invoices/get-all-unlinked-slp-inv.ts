//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getUnlinkedSlpInvoices() {
  const response = await fetch('/api/invoices/unlinked-slp', { method: 'GET' });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getUnlinkedSlpInvoices;
type options = QueryConfigType<QueryFnType>;

export const useGetUnlinkedSlpInvoicesQuery = (config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getUnlinkedSlpInvoices],
    queryFn: getUnlinkedSlpInvoices,
    ...config,
  });
};
