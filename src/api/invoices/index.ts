/* eslint-disable no-constant-condition */

import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const findAllInvoicesByClientId = async (client_id: number) => {
  // Query the 'invoices' table for entries with the provided client_id
  const { data, error } = await supabase
    .from(tableNames.invoices)
    .select(`*`)
    .is('client_id', client_id);
  if (error) throw error;
  return data;
};

// export const validateInvoiceNumber = async (
//   invoiceNum: string
// ): Promise<boolean> => {
//   // Query the 'invoices' table for entries with the provided invoice_number
//   const { data, error } = await supabase
//     .from(tableNames.invoices)
//     .select('*')
//     .eq('invoice_number', invoiceNum); // Use .eq() for equality check

//   if (error) {
//     throw error; // Handle error appropriately
//   }

//   return data?.length > 0;
// };

export const createInvoice = async (invoice: any) => {
  const { error } = await supabase.from(tableNames.invoices).insert(invoice);

  if (error) {
    console.log(error);
  }
  return true;
};

export const updateClientIdOnInvoicesById = async (
  invoice_id: number,
  client_id: number
) => {
  if (!invoice_id || !client_id) throw new Error('Missing Info');

  // Update the 'client_id' for the invoice with the provided 'invoice_id'
  const { error } = await supabase
    .from(tableNames.invoices)
    .update({ client_id: client_id })
    .eq('id', invoice_id);

  if (error) throw error;
};

export const findAllInvoicesWithMissingDuration = async () => {
  const { data, error } = await supabase
    .from(tableNames.invoices)
    .select('*')
    .is('duration', null);
  if (error) throw error;
  return data;
};

export const findAllInvoiceDatesFromInvoices = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .is('invoice_date', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }
    allData = allData.concat(data);
    offset += fetchLimit;
  }
  return allData;
};

export const fetchInvoicesWithLimit = async (fetchLimit = 1000) => {
  let offset = 0;
  let allData: any = [];

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;
    if (data.length === 0) {
      break;
    }
    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

export const findAllInvoices = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
export const findAllInvoicesForUser = async (userId: number) => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .eq('slp_id', userId)
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
