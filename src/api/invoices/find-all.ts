//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

import { queryKey } from '@/constants/query-key';
import {
  IGetInvoicesFilter,
  IGetInvoicesFilterState,
} from '@/store/filters/invoices';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { useRecoilValue } from 'recoil';

// Fetches all invoices with various filters and additional data counts
export async function getAllInvoices(filter: IGetInvoicesFilter) {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const baseUrl = '/api/invoices';

  // Include organization_id in the filter
  const filterWithOrg = {
    ...filter,
    organization_id: org?.id || '',
  };

  const apiUrl = buildUrlWithQueryParams(baseUrl, filterWithOrg);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}
type QueryFnType = typeof getAllInvoices;
type options = QueryConfigType<QueryFnType>;

export const useGetAlInvoicesQuery = (config?: options) => {
  const filter = useRecoilValue(IGetInvoicesFilterState);

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getAllRaw, filter],
    queryFn: () => getAllInvoices(filter),
    ...config,
  });
};
