//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getMaxInvoiceNumber(orgId?: number) {
  const response = await fetch(
    `/api/invoices/max-invoice-number?orgId=${orgId || ''}`,
    {
      method: 'GET',
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch max invoice number');
  }

  const data = await response.json();
  return data?.data;
}

type QueryFnType = typeof getMaxInvoiceNumber;
type options = QueryConfigType<QueryFnType>;

export const useGetMaxInvoiceNumberQuery = (
  orgId?: number,
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-max-invoice-number', orgId], // Include orgId to refetch when it changes
    queryFn: () => getMaxInvoiceNumber(orgId), // Pass orgId to the function
    enabled: Boolean(orgId), // Prevents execution when orgId is falsy
    ...config,
  });
};
