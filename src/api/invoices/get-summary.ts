//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export interface InvoiceSummaryData {
  overdue: {
    count: number;
    total: number;
  };
  dueWithin30: {
    count: number;
    total: number;
  };
  recent: {
    count: number;
    total: number;
  };
  statusCounts: {
    unpaid: number;
    total: number;
  };
}

async function getInvoiceSummary(data: {
  organization_id: number;
  slp_id?: number;
}): Promise<InvoiceSummaryData> {
  const baseUrl = '/api/invoices/summary';
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    organization_id: data.organization_id,
    ...(data.slp_id && { slp_id: data.slp_id }),
  });

  const response = await fetch(apiUrl, {
    method: 'GET',
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoice summary');
  }

  const summaryData = await response.json();
  return summaryData;
}

type QueryFnType = typeof getInvoiceSummary;
type options = QueryConfigType<QueryFnType>;

export const useGetInvoiceSummaryQuery = (
  data: { organization_id: number; slp_id?: number },
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.summary, data],
    queryFn: () => getInvoiceSummary(data),
    ...config,
  });
};
