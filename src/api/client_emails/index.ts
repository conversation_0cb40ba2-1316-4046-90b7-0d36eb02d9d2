/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const findClientEmailByEmail = async (email: string, supabase: any) => {
  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .select(`*`)
    .eq('email', email);
  if (error) throw error;
  return data[0];
};

export const createNewClientEmails = async (
  client_id: number,
  email: string,
  organization_id: any,
  supabase: any
) => {
  if (!email) throw new Error('Missing email');
  if (!client_id) throw new Error('Missing client_id');
  if (!organization_id) throw new Error('Missing organization_id');

  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .insert({
      client_id: client_id,
      email: email?.toLowerCase(),
      is_primary_email: true,
      organization_id: organization_id,
    })
    .select();
  if (error) throw error;
  return data[0];
};

export const findExistingClientEmails = async (email: string) => {
  // Query the 'client_emails' table for entries with the provided email
  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .select(`*`)
    .eq('email', email);

  if (error) throw error;
  return data;
};

export const findAllClientIdsWithNoPrimaryEmail = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.client_emails)
      .select('client_id')
      .eq('is_primary_email', false)
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
export const fetchAllClientsWithPrimaryEmail = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.client_emails)
      .select('client_id')
      .eq('is_primary_email', true)
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

export const findAllClientEmails = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.client_emails)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

export const getClientsWithMultiplePrimaryEmails = async () => {
  // Step 1: Fetch all client emails where is_primary_email is true
  const primaryEmails = await fetchAllClientsWithPrimaryEmail();

  // Step 2: Group by client_id and count occurrences
  const clientEmailCounts = primaryEmails?.reduce((acc: any, email: any) => {
    if (acc[email.client_id]) {
      acc[email.client_id]++;
    } else {
      acc[email.client_id] = 1;
    }
    return acc;
  }, {});

  // Step 3: Filter client IDs with more than one primary email
  const clientIdsWithMultiplePrimaryEmails = Object.keys(
    clientEmailCounts
  ).filter((clientId) => clientEmailCounts[clientId] > 1);

  return clientIdsWithMultiplePrimaryEmails;
};

export const findEmailByClientId = async (clientId: number) => {
  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .select('*')
    .eq('client_id', clientId);
  if (error) throw error;
  return data;
};
