import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const deleteEmail = async (body: any) => {
  // const response = await fetch(`/api/send-email`, {
  const response = await fetch(`/api/send-email/db`, {
    method: 'DELETE',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof deleteEmail;

export const useDeleteEmailApi = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: (res) => {
      toaster.create({
        type: 'success',
        description: res?.message || 'Email deleted successfully',
      });
      queryClient.invalidateQueries([queryKey.emails.readDBEmails]);
    },
    retry: false,
    mutationKey: ['delete-email'],
    mutationFn: deleteEmail,
    ...config,
  });
};
