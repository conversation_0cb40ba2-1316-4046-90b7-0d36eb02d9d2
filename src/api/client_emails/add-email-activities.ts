import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const addEmailActivities = async (body: any) => {
  const response = await fetch(`/api/email-activities`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof addEmailActivities;

export const useAddEmailActivitiesApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: ToastMessages.somethingWrong || err?.message,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: '',
    //   });
    // },
    retry: false,
    mutationKey: ['add-email-activities'],
    mutationFn: addEmailActivities,
    ...config,
  });
};
