import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateEmailActivities = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/email-activities/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ payload: body.data }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating invoices data');
  }
  return response.json();
};

type QueryFnType = typeof updateEmailActivities;

export const useUpdateEmailActivitiesApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: '',
    //   });
    // },
    retry: false,
    mutationKey: ['update-email-activities'],
    mutationFn: updateEmailActivities,
    ...config,
  });
};
