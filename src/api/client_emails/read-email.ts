import { query<PERSON><PERSON> } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

type IGetEmailsFilter = {
  maxResults?: string;
  query: string;
  page?: number;
  pageSize?: number;
};

// async function getEmails(filter: IGetEmailsFilter) {
//   const baseUrl = `/api/send-email/gmail`;
//   const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
//   const response = await fetch(apiUrl, {
//     method: 'GET',
//   });
//   const data: any = await response.json();
//   return data;
// }
async function getEmails(filter: IGetEmailsFilter) {
  const baseUrl = `/api/send-email/gmail`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
    });

    const data = await response.json();
    return data;
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('Request aborted by timeout');
    } else {
      console.error('Request failed:', error);
    }
    throw error;
  }
}

type QueryFnType = typeof getEmails;

export const useGetEmailsQuery = (
  filter: IGetEmailsFilter,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.emails.readEmails, filter],
    queryFn: () => getEmails(filter),
    ...config,
  });
};
