import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

type TBody = {
  [key: string]: boolean;
};

const updateGmailAccess = async (body: TBody) => {
  const response = await fetch(`/api/integrations`, {
    method: 'PATCH',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating invoices data');
  }
  return response.json();
};

type QueryFnType = typeof updateGmailAccess;

export const useUpdateGmailAccessApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Gmail access successfully disconnected',
      });
    },
    retry: false,
    mutationKey: ['update-google-access'],
    mutationFn: updateGmailAccess,
    ...config,
  });
};
