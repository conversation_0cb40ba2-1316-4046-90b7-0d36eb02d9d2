//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  QueryConfigType,
  useQuery,
  ExtractFnReturnType,
} from '@/lib/react-query';

export const fetchFormById = async (id: number) => {
  //console.log('useQuery:', useQuery);
  const response = await fetch(`/api/forms/${id}`);
  if (!response.ok) throw new Error('Error fetching form data');
  return response.json();
};

type QueryFnType = typeof fetchFormById;

type options = QueryConfigType<QueryFnType>;

export const useGetFormByIdQuery = (id: number, config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.forms.getFormById, id],
    queryFn: () => fetchFormById(id),
    ...config,
  });
};
