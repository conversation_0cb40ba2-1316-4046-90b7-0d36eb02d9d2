//OPTIMIZED
import { query<PERSON>ey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { IGetFormsFilter, IGetFormsFilterState } from '@/store/filters/forms';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { useRecoilValue } from 'recoil';

export async function getAllForms(filter: IGetFormsFilter) {
  const baseUrl = '/api/forms';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllForms;

export const useGetAllFormsQuery = (
  organizationId: any,
  config?: QueryConfigType<QueryFnType>
) => {
  const filter = useRecoilValue(IGetFormsFilterState);
  // console.log('filter', filter);
  // console.log('organizationId---3', organizationId);

  // Create a modified filter with the organization_id
  const modifiedFilter = {
    ...filter,
    organization_id: organizationId?.organizationId,
  };

  // console.log('modifiedFilter', modifiedFilter);

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.forms.getAllForms, modifiedFilter],
    queryFn: () => getAllForms(modifiedFilter),
    ...config,
  });
};
