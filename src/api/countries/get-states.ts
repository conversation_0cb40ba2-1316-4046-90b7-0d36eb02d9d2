import { QueryConfigType, useQuery } from '@/lib/react-query';
import { State } from 'country-state-city';

async function getStateByCountryCode(countryCode: any) {
  const states = State?.getStatesOfCountry(countryCode);
  return states;
}
type QueryFnType = typeof getStateByCountryCode;

export const useGetStatesQuery = (
  countryCode: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['getStateByCountryCode', countryCode],
    queryFn: () => getStateByCountryCode(countryCode),
    ...config,
  });
};
