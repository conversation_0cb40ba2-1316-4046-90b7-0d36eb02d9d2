import { QueryConfigType, useQuery } from '@/lib/react-query';
import { Country } from 'country-state-city';

async function getCountries() {
  const countries = Country?.getAllCountries();
  return countries;
}
type QueryFnType = typeof getCountries;

export const useGetCountriesQuery = (config?: QueryConfigType<QueryFnType>) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['getAllCountries'],
    queryFn: () => getCountries(),
    ...config,
  });
};
