import { QueryConfigType, useQuery } from '@/lib/react-query';
import { City } from 'country-state-city';

async function getCitiesOfState(countryCode: any, stateCode: any) {
  const cities = City?.getCitiesOfState(countryCode, stateCode);
  return cities;
}
type QueryFnType = typeof getCitiesOfState;

export const useGetCitiesOfStateQuery = (
  countryCode: any,
  stateCode: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['getCitiesOfState', countryCode, stateCode],
    queryFn: () => getCitiesOfState(countryCode, stateCode),
    ...config,
  });
};
