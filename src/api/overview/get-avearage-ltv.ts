import { useQuery } from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';
import { IUser } from '@/shared/interface/user';

type DashboardStatsResponse = {
  slp_name: string;
  average_ltv: number;
  average_invoice_count: number;
  paying_clients: number;
  total_invoice_count: number;
  total_client_count: number;
  total_revenue: number;
};

async function fetchDashboardStats(
  slp: IUser
): Promise<DashboardStatsResponse> {
  const organizationId = slp?.organization_id;
  const slpId = slp?.id;

  if (!organizationId) {
    throw new Error('Missing organization ID from slp');
  }

  if (!slpId) {
    throw new Error('Missing SLP ID');
  }

  const url = new URL('/api/average-ltv', window.location.origin);
  url.searchParams.set('organization_id', organizationId.toString());
  url.searchParams.set('slp_id', slpId.toString());

  const res = await fetch(url.toString(), { method: 'GET' });
  const json = await res.json();

  if (!res.ok) {
    throw new Error(json.message || 'Failed to fetch dashboard stats');
  }

  return json;
}

export const useDashboardStatsQuery = (slp: IUser | undefined) => {
  return useQuery<DashboardStatsResponse>({
    queryKey: [queryKey.overview.aveargeLtv, slp?.id],
    queryFn: () => fetchDashboardStats(slp!),
    enabled: !!slp?.id,
  });
};
