import { useQuery } from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';

type OrgStatsResponse = {
  average_ltv: number;
  average_invoice_count: number;
  paying_clients: number;
  total_invoice_count: number;
  total_client_count: number;
  total_revenue: number;
};

async function fetchOrgStats(orgId: number): Promise<OrgStatsResponse> {
  if (!orgId) {
    throw new Error('Missing organization ID');
  }

  const url = new URL(
    '/api/overview/admin-average-ltv',
    window.location.origin
  );
  url.searchParams.set('organization_id', orgId.toString());

  const res = await fetch(url.toString(), { method: 'GET' });
  const json = await res.json();

  if (!res.ok) {
    throw new Error(json.message || 'Failed to fetch organization stats');
  }

  return json;
}

export const useAdminAverageLtvQuery = () => {
  const getOrgId = () => {
    if (typeof window === 'undefined') return null;

    const raw = localStorage.getItem('UserState');
    const dataOrg = raw ? JSON.parse(raw) : null;
    const org = dataOrg?.UserState?.organization;

    return org?.id ? Number(org.id) : null;
  };

  const orgId = getOrgId();

  return useQuery<OrgStatsResponse>({
    queryKey: [queryKey.overview.adminaveargeLtv, orgId],
    queryFn: () => fetchOrgStats(orgId!),
    enabled: typeof window !== 'undefined' && !!orgId,
  });
};
