import { MutationConfig, useMutation } from '@/lib/react-query';
import { toaster } from '@/components/ui/toaster';

const uploadDoc = async (body: FormData) => {
  const response = await fetch(`/api/clients/document`, {
    method: 'POST',
    body: body,
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof uploadDoc;

export const useUploadClientDocApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: () => {
      toaster.create({ type: 'error', description: 'An error occurred' });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Document uploaded successfully',
      });
    },
    retry: false,
    mutationKey: ['upload-document-client'],
    mutationFn: uploadDoc,
    ...config,
  });
};
