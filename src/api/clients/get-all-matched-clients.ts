//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { IAllClientsMatchedFilterState } from '@/store/filters/clients';

async function getAllMatchedClients(
  filter: Partial<IAllClientsMatchedFilterState> | undefined
) {
  const baseUrl = `/api/clients/search/matched`;

  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch all matched clients');
  }
  const { data } = await response.json();

  return data;
}

type QueryFnType = typeof getAllMatchedClients;

type options = QueryConfigType<QueryFnType>;
export const useGetAllMatchedClientsQuery = (
  filter?: Partial<IAllClientsMatchedFilterState> | undefined,
  uniqueKey?: string,
  config?: options
) => {
  const finalQueryKey = uniqueKey
    ? ['fetch-all-matched-clients', filter, uniqueKey] // Include uniqueKey in queryKey
    : ['fetch-all-matched-clients', filter];
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: finalQueryKey,
    queryFn: () => getAllMatchedClients(filter),
    ...config,
  });
};
