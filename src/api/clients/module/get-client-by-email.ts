import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getClientByEmail(email: string, organization_id: any) {
  const response = await fetch(
    `/api/public/clients/email/${email}/${organization_id}`,
    {
      method: 'GET',
      cache: 'no-store',
    }
  );

  const json = await response.json();
  return json;
}

type QueryFnType = typeof getClientByEmail;

export const useGetClientByEmailQueryCM = (
  data: {
    email: string;
    organization_id?: string;
  },
  config?: QueryConfigType<QueryFnType>
) => {
  const { email, organization_id } = data;
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.client.getByEmail, email, organization_id],
    queryFn: () => getClientByEmail(email, organization_id),
    ...config,
  });
};
