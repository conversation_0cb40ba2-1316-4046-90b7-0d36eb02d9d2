import { query<PERSON><PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export const useGetClientInvoicesQueryCM = (
  data: {
    id: string;
    organization_id?: string;
  },
  config?: QueryConfigType<any>
) => {
  const { id, organization_id } = data;
  return useQuery<ExtractFnReturnType<any>>({
    queryKey: [queryKey.client.getInvoices, id, organization_id],
    queryFn: async () => {
      const response = await fetch(
        `/api/public/clients/invoices/${id}/${organization_id}`,
        {
          method: 'GET',
          cache: 'no-store',
        }
      );

      const json = await response.json();
      return json;
    },
    ...config,
  });
};
