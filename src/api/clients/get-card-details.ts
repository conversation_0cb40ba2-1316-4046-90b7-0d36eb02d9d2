import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

const getCardDetails = async (body: any) => {
  const response = await fetch(`/api/stripe/get-payment-methods`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof getCardDetails;

type options = QueryConfigType<QueryFnType>;

export const useGetCardDetailsQuery = (data: any, config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.client.getCardDetails, data],
    queryFn: () => getCardDetails(data),
    ...config,
  });
};
