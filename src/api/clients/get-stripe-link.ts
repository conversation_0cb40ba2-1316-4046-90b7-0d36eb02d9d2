import { MutationConfig, useMutation } from '@/lib/react-query';
import { toaster } from '@/components/ui/toaster';

const getStripeLink = async (body: any) => {
  const response = await fetch(`/api/stripe/create-setup-session`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof getStripeLink;

export const useGetClientStripeLinkMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: () => {
      toaster.create({ type: 'error', description: 'An error occurred' });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Document uploaded successfully',
      });
    },
    retry: false,
    mutationKey: ['get-stripe-link'],
    mutationFn: getStripeLink,
    ...config,
  });
};
