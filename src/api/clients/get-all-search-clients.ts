import supabase from '@/lib/supabase/client';
import { MutationConfig, useMutation } from '@/lib/react-query';
import { useRecoilState } from 'recoil';
import {
  AllClientsFilterState,
  IAllClientsFilterState,
} from '@/store/filters/clients';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const fetchClients = async (filter: Partial<IAllClientsFilterState>) => {
  const { search, currentPage = 1, size } = filter;

  const formattedSearch = search?.trim().replace(/\s/g, '') || null;
  const page_limit = size || 50;

  const { data, error } = await supabase.rpc('search_clients_real', {
    provinces: null,
    lead: null,
    stages: null,
    slp_values: null,
    goal: null,
    page_limit,
    page_offset: (currentPage - 1) * page_limit,
    search_text: formattedSearch,
  });

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

type QueryFnType = typeof fetchClients;

export const useFetchClientsApi = (config?: MutationConfig<QueryFnType>) => {
  const [filter] = useRecoilState(AllClientsFilterState);

  return useMutation({
    mutationFn: () => fetchClients(filter),
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false, // Disable retries
    mutationKey: ['fetch-clients'],
    ...config,
  });
};
