//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  QueryConfigType,
  useQuery,
  ExtractFnReturnType,
} from '@/lib/react-query';
import { FullClient } from '@/shared/interface/clients';

export const fetchClientById = async (id: number): Promise<FullClient> => {
  const response = await fetch(`/api/clients/${id}`);
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof fetchClientById;

type options = QueryConfigType<QueryFnType>;

export const useGetClientByIdQuery = (id: number, config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.client.getById, id],
    queryFn: () => fetchClientById(id),
    ...config,
  });
};
