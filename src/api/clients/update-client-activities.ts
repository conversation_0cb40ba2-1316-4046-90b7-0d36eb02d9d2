//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateClientActivities = async (body: {
  data: Partial<any>;
  id: number;
}) => {
  const response = await fetch(`/api/clients/${body.id}/activities`, {
    method: 'POST',
    body: JSON.stringify(body.data),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof updateClientActivities;

export const useUpdateClientActivitiesMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.clients.updateSuccess,
      });
    },
    retry: false,
    mutationKey: ['update-client-activities'],
    mutationFn: updateClientActivities,
    ...config,
  });
};
