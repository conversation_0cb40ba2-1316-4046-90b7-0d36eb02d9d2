import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
export interface ISearchClientFilter {
  search_text: string;
  org_id?: string;
  page_limit: number;
  page_offset: number;
}
async function newSearchClients(
  filter: Partial<ISearchClientFilter> | undefined
) {
  const baseUrl = `/api/clients/search/new`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch clients');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof newSearchClients;

type options = QueryConfigType<QueryFnType>;

export const useNewSearchClientsQuery = (
  filter?: Partial<ISearchClientFilter> | undefined,
  uniqueKey?: string,
  config?: options
) => {
  // Get org_id from URL if not in filter
  const getOrgId = () => {
    if (filter?.org_id) return filter.org_id;
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      return params.get('organization_id')
        ? Number(params.get('organization_id'))
        : null;
    }
    return null;
  };

  const finalFilter = {
    ...filter,
    org_id: filter?.org_id ?? getOrgId(),
  };

  const finalQueryKey = uniqueKey
    ? [queryKey.client.newSearch, finalFilter, uniqueKey]
    : [queryKey.client.newSearch, finalFilter];

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry: false,
    queryKey: finalQueryKey,
    queryFn: () => newSearchClients(finalFilter as any),
    ...config,
  });
};
