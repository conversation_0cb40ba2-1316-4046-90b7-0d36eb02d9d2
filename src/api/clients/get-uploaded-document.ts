import { queryKey } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getUploadedDocument(payload: any) {
  const baseUrl = `/api/clients/document`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, payload);
  const response = await fetch(apiUrl, { method: 'GET' });
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getUploadedDocument;

export const useGetClientUploadedDocumentQuery = (
  data: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.client.getClientDocuments, data],
    queryFn: () => getUploadedDocument(data),
    ...config,
  });
};
