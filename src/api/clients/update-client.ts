//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { IClient } from '@/shared/interface/clients';
import { toaster } from '@/components/ui/toaster';

const updateClient = async (body: {
  data: Partial<IClient>;
  id: number;
  initialData?: any;
}) => {
  const response = await fetch(`/api/clients/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ payload: body.data, initialData: body.initialData }),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof updateClient;

export const useUpdateClientMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toaster.create({
    //     type: 'success',
    //     description: ToastMessages.clients.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: ['update-client'],
    mutationFn: updateClient,
    ...config,
  });
};
