import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

interface FollowUpQueryParams {
  page?: number;
  limit?: number;
  loadAll?: boolean;
}

export async function getUncompletedFollowups(
  params: FollowUpQueryParams = {}
) {
  const { page = 1, limit = 10, loadAll = false } = params;

  const searchParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    loadAll: loadAll.toString(),
  });

  const response = await fetch(`/api/follow-up/uncompleted?${searchParams}`, {
    method: 'GET',
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch follow ups');
  }

  const data = await response.json();
  return data;
}

type QueryFnType = typeof getUncompletedFollowups;
type Options = QueryConfigType<QueryFnType> & FollowUpQueryParams;

export const useGetAllFollowUpQuery = (config: Options = {}) => {
  const { page, limit, loadAll, ...queryConfig } = config;

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.followUp.getAllFollowup, { page, limit, loadAll }],
    queryFn: () => getUncompletedFollowups({ page, limit, loadAll }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    ...queryConfig,
  });
};
