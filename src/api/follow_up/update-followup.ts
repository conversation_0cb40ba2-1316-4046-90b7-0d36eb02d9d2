//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { IFollowup } from '@/shared/interface/followup';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const updateFollowup = async (body: {
  data: Partial<IFollowup>;
  id: number;
}) => {
  const response = await fetch(`/api/follow-up/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify(body.data),
  });

  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof updateFollowup;

export const useUpdateFollowUpMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.clients.updateSuccess,
      });
    },
    retry: false,
    mutationKey: [queryKey.followUp.updateFollowUp],
    mutationFn: updateFollowup,
    ...config,
  });
};
