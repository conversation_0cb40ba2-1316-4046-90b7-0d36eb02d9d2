/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const findAllFollowUps = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.followups)
      .select(
        '*, clients(first_name, last_name, stage, province, lead_quality, notes, phone, client_emails(email))'
      )
      .neq('status', 'Completed')
      .order('followup_date', { ascending: true })
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
export const updateIncompleteFollowUp = async (
  data: any,
  contactId: number
) => {
  const { error } = await supabase
    .from('followups')
    .update(data)
    .eq('client_id', contactId)
    .eq('status', 'Incomplete');

  if (error) throw error;
};
