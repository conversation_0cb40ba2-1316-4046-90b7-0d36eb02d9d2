//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

export interface IAddFollowup {
  followup_date: string;
  client_id: number;
  status: string;
  contact_by: string;
}

const addNewRecord = async (body: IAddFollowup | any) => {
  const response = await fetch('api/follow-up', {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof addNewRecord;
export const useAddFollowUpMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (error: any) => {
      toaster.create({
        type: 'error',
        description: error.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.operationSuccess,
      });
    },
    retry: false,
    mutationKey: [queryKey.followUp.addNewFollowup],
    mutationFn: addNewRecord,
    ...config,
  });
};
