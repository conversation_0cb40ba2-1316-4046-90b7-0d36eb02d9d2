import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

export const getAllEvents = async (userFromServer: any) => {
  const supabase = createSupabaseServer();
  const { data, error } = await supabase
    .from(tableNames.events)
    .select('*, availability:availabilities(day, start_time, end_time)')
    .eq('is_deleted', false)
    .eq('is_active', true)
    .eq('creator_slug', userFromServer?.event_slug);

  if (error) {
    throw error;
  }
  return data;
};
export const getAllEventsById = async (userFromServer: any) => {
  const supabase = createSupabaseServer();
  const { data, error } = await supabase
    .from(tableNames.events)
    .select('*, availability:availabilities(day, start_time, end_time)')
    .eq('is_deleted', false)
    .eq('is_active', true)
    .eq('creator_id', userFromServer?.id);

  if (error) {
    throw error;
  }
  return data;
};
