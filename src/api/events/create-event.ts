//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

export const createEventApi = async (body: any) => {
  const response = await fetch(`/api/events`, {
    method: 'POST',
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating events');
  }

  return await response.json();
};

type QueryFnType = typeof createEventApi;

export const useCreateEventMutatuion = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: [queryKey.events.create],
    mutationFn: createEventApi,
    ...config,
  });
};
