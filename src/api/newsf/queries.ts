import {
  ExtractFnReturnType,
  MutationConfig,
  QueryConfigType,
  useMutation,
  useQuery,
} from '@/lib/react-query';
import {
  createInvoice,
  createTax,
  deleteInvoiceById,
  deleteTaxById,
  getAllInvoices,
  getAllPurchases,
  getAllTaxes,
  getInvoiceById,
  getTaxById,
  linkInvoiceWithTransaction,
  updateInvoiceById,
  updatePurchaseById,
  updateTaxById,
} from './service';
import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';

export const useGetInvoiceByIdQuery = (
  id: any,
  config?: QueryConfigType<typeof getInvoiceById>
) => {
  return useQuery<ExtractFnReturnType<typeof getInvoiceById>>({
    queryKey: ['newsf-get-invoice-by-id', id],
    queryFn: () => getInvoiceById(id),
    ...config,
  });
};

export const useGetAllInvoicesQuery = (
  filter: any,
  config?: QueryConfigType<typeof getAllInvoices>
) => {
  return useQuery<ExtractFnReturnType<typeof getAllInvoices>>({
    queryKey: [queryKey.newSf.getAllInvoices, filter],
    queryFn: () => getAllInvoices(filter),
    ...config,
  });
};

export const useUpdateInvoiceMutation = (
  config?: MutationConfig<typeof updateInvoiceById>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-update-invoice'],
    mutationFn: updateInvoiceById,
    ...config,
  });
};
export const useCreateInvoiceMutation = (
  config?: MutationConfig<typeof createInvoice>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-create-invoice'],
    mutationFn: createInvoice,
    ...config,
  });
};
export const useLinkInvoiceWithTransactionMutation = (
  config?: MutationConfig<typeof linkInvoiceWithTransaction>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-link-invoice-transaction'],
    mutationFn: linkInvoiceWithTransaction,
    ...config,
  });
};

export const useDeleteInvoiceMutation = (
  config?: MutationConfig<typeof deleteInvoiceById>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Invoice deleted successfully',
      });
    },
    mutationKey: ['newsf-delete-invoice'],
    mutationFn: deleteInvoiceById,
    ...config,
  });
};

// ============= TAX QUERIES ==========================

export const useGetTaxByIdQuery = (
  id: any,
  config?: QueryConfigType<typeof getTaxById>
) => {
  return useQuery<ExtractFnReturnType<typeof getTaxById>>({
    queryKey: ['newsf-get-tax-by-id', id],
    queryFn: () => getTaxById(id),
    ...config,
  });
};

export const useGetAllTaxesQuery = (
  filter: any,
  config?: QueryConfigType<typeof getAllTaxes>
) => {
  return useQuery<ExtractFnReturnType<typeof getAllTaxes>>({
    queryKey: ['newsf-get-all-tax', filter],
    queryFn: () => getAllTaxes(filter),
    ...config,
  });
};

export const useCreateTaxMutation = (
  config?: MutationConfig<typeof createTax>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-create-tax'],
    mutationFn: createTax,
    ...config,
  });
};

export const useUpdateTaxMutation = (
  config?: MutationConfig<typeof updateTaxById>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-update-tax'],
    mutationFn: updateTaxById,
    ...config,
  });
};

export const useDeleteTaxMutation = (
  config?: MutationConfig<typeof deleteTaxById>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-delete-tax'],
    mutationFn: deleteTaxById,
    ...config,
  });
};

// ========================= PURCHASES ====================

export const useGetAllPurchases = (
  filter: any,
  config?: QueryConfigType<typeof getAllPurchases>
) => {
  return useQuery<ExtractFnReturnType<typeof getAllInvoices>>({
    queryKey: [queryKey.newSf.getAllPurchases, filter],
    queryFn: () => getAllPurchases(filter),
    ...config,
  });
};

export const useUpdatePurchaseMutation = (
  config?: MutationConfig<typeof updatePurchaseById>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['newsf-update-purchase'],
    mutationFn: updatePurchaseById,
    ...config,
  });
};
