import { KlaviyoEvent } from '@/lib/klaviyo/api';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getTrackableEvents(organization_id: any) {
  const baseUrl = '/api/klaviyo-trackable-events';
  const apiUrl = buildUrlWithQueryParams(baseUrl, { organization_id });

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch events');
  }
  const data = await response.json();
  return data;
}
export async function updateTrackableEvents(data: {
  organization_id: any;
  payload: any;
}) {
  const baseUrl = '/api/klaviyo-trackable-events';
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    organization_id: data.organization_id,
  });

  const response = await fetch(apiUrl, {
    method: 'PATCH',
    body: JSON.stringify(data.payload),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update trackable events');
  }
  return await response.json();
}
export async function trackAnEvent(data: {
  organization_id: any;
  event: KlaviyoEvent;
  action: string;
}) {
  const response = await fetch('/api/klaviyo/track-event', {
    method: 'POST',
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to track events');
  }
  return await response.json();
}
