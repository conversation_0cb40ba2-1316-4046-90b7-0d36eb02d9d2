import {
  ExtractFnReturnType,
  MutationConfig,
  QueryConfigType,
  useMutation,
  useQuery,
} from '@/lib/react-query';

import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { getTrackableEvents, updateTrackableEvents } from './service';
import {
  getKlaviyoOrganization,
  updateKlaviyoOrganization,
} from '@/lib/klaviyo/service';
import { SupabaseClient } from '@supabase/supabase-js';

export const useGetAllTrackableEventQuery = (
  id: any,
  config?: QueryConfigType<typeof getTrackableEvents>
) => {
  return useQuery<ExtractFnReturnType<typeof getTrackableEvents>>({
    queryKey: [queryKey.trackableEvents.getAll, id],
    queryFn: () => getTrackableEvents(id),
    ...config,
  });
};
export const useGetKlaviyoOrganization = (
  id: any,
  supabase: SupabaseClient,
  config?: QueryConfigType<typeof getKlaviyoOrganization>
) => {
  return useQuery<ExtractFnReturnType<typeof getKlaviyoOrganization>>({
    queryKey: [queryKey.klaviyoOrganization.getAll, id],
    queryFn: () => getKlaviyoOrganization(id, supabase),
    ...config,
  });
};

export const useUpdateTrackableEventMutation = (
  config?: MutationConfig<typeof updateTrackableEvents>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['update-trackable-event'],
    mutationFn: updateTrackableEvents,
    ...config,
  });
};

export const useUpdateKlaviyoOrganization = (
  config?: MutationConfig<typeof updateKlaviyoOrganization>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    mutationKey: ['update-updateKlaviyoOrganization-event'],
    mutationFn: updateKlaviyoOrganization,
    ...config,
  });
};
