//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getLinkedClients(id: number) {
  const response = await fetch(`/api/linked-clients/${id}`, { method: 'GET' });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch linked clients');
  }
  return json;
}

type QueryFnType = typeof getLinkedClients;

export const useGetLinkedClientsQuery = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },

    queryKey: [queryKey.linkedClients.getByClientId, id],
    queryFn: () => getLinkedClients(id),
    ...config,
  });
};
