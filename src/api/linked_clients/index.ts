import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const findLinkedClientById = async (id: number) => {
  const { data, error } = await supabase
    .from(tableNames.linked_clients)
    .select(`*`)
    .eq('id', id);
  if (error) throw error;
  return data;
};

export const createLinkedClient = async (linkedData: any) => {
  const { data, error } = await supabase
    .from(tableNames.linked_clients)
    .insert(linkedData);
  if (error) throw error;
  return data;
};

export const findAllClientsByClientId = async (clientId: number) => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  // eslint-disable-next-line no-constant-condition
  while (true) {
    const { data, error } = await supabase
      .from(tableNames.linked_clients)
      .select('*')
      .eq('client_id', clientId)
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
