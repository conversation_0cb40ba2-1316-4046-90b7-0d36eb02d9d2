import { query<PERSON><PERSON> } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { AllOrganizationFilterState } from '@/store/filters/organizations';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { useRecoilState } from 'recoil';

async function getOrganization(filter: any) {
  const baseUrl = `/api/organizations`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, { method: 'GET' });
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getOrganization;

export const useGetOrganizationQuery = (
  config?: QueryConfigType<QueryFnType>
) => {
  const [filter] = useRecoilState(AllOrganizationFilterState);
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.organizations.getAll],
    queryFn: () => getOrganization(filter),
    ...config,
  });
};
