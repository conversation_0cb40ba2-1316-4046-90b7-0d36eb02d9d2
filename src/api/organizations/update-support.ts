import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateSupport = async (body: any) => {
  const response = await fetch(`/api/support`, {
    method: 'PATCH',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating tag data');
  }
  return response.json();
};

type QueryFnType = typeof updateSupport;

export const useUpdateSupportApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Support updated successfully',
      });
    },
    retry: false,
    mutationKey: ['update-support'],
    mutationFn: updateSupport,
    ...config,
  });
};
