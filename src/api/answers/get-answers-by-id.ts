import { queryKey } from '@/constants/query-key';
import {
  QueryConfigType,
  useQuery,
  ExtractFnReturnType,
} from '@/lib/react-query';

type FetchAnswersParams = {
  id: number;
  filterBy?: 'id' | 'form_id';
};

export const fetchAnswers = async ({
  id,
  filterBy = 'id',
}: FetchAnswersParams) => {
  const response = await fetch(`/api/answers/${id}?filterBy=${filterBy}`);
  if (!response.ok) throw new Error('Error fetching form data');
  return response.json();
};

type QueryFnType = typeof fetchAnswers;

type Options = QueryConfigType<QueryFnType>;

export const useGetAnswersByIdQuery = (
  params: FetchAnswersParams,
  config?: Options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.forms.getFormAnswersById, params.id, params.filterBy],
    queryFn: () => fetchAnswers(params),
    ...config,
  });
};
