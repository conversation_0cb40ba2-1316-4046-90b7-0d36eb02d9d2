//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import { IGetAnswerFilterState } from '@/store/filters/answers';

import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { useRecoilValue } from 'recoil';

export const fetchAllAnswers = async (filter: any) => {
  const baseUrl = '/api/answers';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) throw new Error('Error fetching form data');
  return response.json();
};

type QueryFnType = typeof fetchAllAnswers;

type options = QueryConfigType<QueryFnType>;

export const useGetAllAnswersQuery = (
  organizationId: any,
  config?: options
) => {
  const filter = useRecoilValue(IGetAnswerFilterState);
  // console.log('filter', filter);
  // console.log('organizationId---3', organizationId);

  // Create a modified filter with the organization_id
  const modifiedFilter = {
    ...filter,
    organization_id: organizationId?.organizationId,
  };
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.forms.getFormAnswers],
    queryFn: () => fetchAllAnswers(modifiedFilter),
    ...config,
  });
};
