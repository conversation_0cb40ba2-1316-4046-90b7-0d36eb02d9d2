import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import {
  MutationConfig,
  useMutation,
  //   useQuery,
} from '@/lib/react-query';
import supabase from '@/lib/supabase/client';

export async function getPayPeriodBookings(slpId: number) {
  const currentDate = new Date();
  const firstDay = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  );
  const lastDay = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  );
  const firstDayStr = firstDay
    .toISOString()
    .replace('T', ' ')
    .replace('Z', '+00');
  const lastDayStr = lastDay
    .toISOString()
    .replace('T', ' ')
    .replace('Z', '+00');

  const { data, error }: any = await supabase
    .from(tableNames.bookings)
    .select('*')
    .eq('slp_id', slpId)
    .gte('created_at', firstDayStr)
    .lte('created_at', lastDayStr);

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

type QueryFnType = typeof getPayPeriodBookings;
export const useGetPayPeriodBookingsApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err.message || 'An error occurred',
      });
    },

    retry: false,
    mutationKey: ['get-pay-period-bookings'],
    mutationFn: getPayPeriodBookings,
    ...config,
  });
};
