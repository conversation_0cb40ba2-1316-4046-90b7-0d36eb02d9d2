//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
// import { IBookings } from '@/shared/interface/consultation';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const updateBooking = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/bookings/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ payload: body.data }),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof updateBooking;

export const useUpdateBookingMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.bookings.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.bookings.updateBooking],
    mutationFn: updateBooking,
    ...config,
  });
};
