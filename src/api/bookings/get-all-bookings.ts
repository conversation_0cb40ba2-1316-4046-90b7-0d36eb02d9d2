//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getAllBookings({
  event,
  current_page,
  page_size,
  email,
}: {
  event: any;
  current_page: number;
  page_size: number;
  email?: string;
}) {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  const baseUrl = `/api/bookings`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    event,
    current_page,
    page_size,
    email,
    organization_id: org?.id,
  });

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch bookings');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllBookings;

type options = QueryConfigType<QueryFnType>;
export const useGetAllBookingsQuery = (
  data: {
    event: any;
    current_page: number;
    page_size: number;
    email?: string;
  },
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.bookings.getAllBookings, data],
    queryFn: () => getAllBookings(data),
    ...config,
  });
};
