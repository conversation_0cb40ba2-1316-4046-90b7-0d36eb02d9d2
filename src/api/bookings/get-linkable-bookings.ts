import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getLinkableBookings(client_id: number) {
  const response = await fetch(`/api/bookings/linkable/${client_id}`);
  if (!response.ok) throw new Error('Error fetching linkable bookings');
  return response.json();
}

type QueryFnType = typeof getLinkableBookings;

export const useGetLinkableBookingsQuery = (
  client_id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.bookings.getLinkable, client_id],
    queryFn: () => getLinkableBookings(client_id),
    enabled: !!client_id,
    ...config,
  });
};
