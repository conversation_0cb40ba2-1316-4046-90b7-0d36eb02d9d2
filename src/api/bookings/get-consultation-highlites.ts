import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

import { buildUrlWithQueryParams } from '@/utils/build-url-query';

// Define timezone as optional in the input type
interface GetConsultationHighliteData {
  year?: any; // Make it optional
}

async function getConsultationHighlites(data: GetConsultationHighliteData) {
  const baseUrl = '/api/bookings/consultation-highlites';
  const apiUrl = buildUrlWithQueryParams(baseUrl, data);
  const response = await fetch(apiUrl, { method: 'GET' });

  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json;
}

type QueryFnType = typeof getConsultationHighlites;

export const useGetConsultationHighLitesQuery = (
  data: GetConsultationHighliteData, // Updated type with optional timezone
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      if (failureCount < 1) return true;
      return false;
    },
    queryKey: ['get-consultation-highlites', data?.year],
    queryFn: () => getConsultationHighlites(data), // Correct

    ...config,
  });
};
