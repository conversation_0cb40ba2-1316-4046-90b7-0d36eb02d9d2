//OPTIMIZED
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation } from '@/lib/react-query';

const createBooking = async (body: any) => {
  const response = await fetch(`/api/bookings`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating booking');
  return response.json();
};

type QueryFnType = typeof createBooking;

export const useCreateBookingMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.bookings.createSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.bookings.createBooking],
    mutationFn: createBooking,
    ...config,
  });
};
