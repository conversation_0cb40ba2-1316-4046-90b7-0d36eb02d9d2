//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { IAllConsultationsFilterState } from '@/store/filters/consultations';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getAllConsultations(
  filter: Partial<IAllConsultationsFilterState>
) {
  const baseUrl = `/api/consultations`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch consultations');
  }
  const data = await response.json();

  return data;
}
type QueryFnType = typeof getAllConsultations;
type options = QueryConfigType<QueryFnType>;

export const useGetAllConsultationsQuery = (
  filter: Partial<IAllConsultationsFilterState>,
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.consultations.getAllConsultations, filter],
    queryFn: () => getAllConsultations(filter),
    ...config,
  });
};
