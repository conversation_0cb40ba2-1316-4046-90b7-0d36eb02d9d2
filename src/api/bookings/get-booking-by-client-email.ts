//OPTIMIZED
import { queryK<PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getBookingByClientEmail(email: string) {
  const response = await fetch(`/api/bookings/email?${email}`);
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
}

type QueryFnType = typeof getBookingByClientEmail;

export const useGetBookingByEmailQuery = (
  email: string,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.bookings.getByClient, email],
    queryFn: () => getBookingByClientEmail(email),
    ...config,
  });
};
