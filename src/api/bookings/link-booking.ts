import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const linkBooking = async (body: any) => {
  const response = await fetch(`/api/bookings/link-booking`, {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Error linking booking');
  }

  return response.json();
};

type QueryFnType = typeof linkBooking;

export const useLinkBookingMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    mutationKey: [queryKey.bookings.linkBooking],
    mutationFn: linkBooking,
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Booking linked successfully!',
      });
    },
    retry: false,
    ...config,
  });
};
