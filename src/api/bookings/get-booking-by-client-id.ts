//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getBookingByClientId(id: number) {
  const response = await fetch(`/api/bookings/client/${id}`);
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
}

type QueryFnType = typeof getBookingByClientId;

export const useGetBookingByClientQuery = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.bookings.getByClient, id],
    queryFn: () => getBookingByClientId(id),
    ...config,
  });
};
