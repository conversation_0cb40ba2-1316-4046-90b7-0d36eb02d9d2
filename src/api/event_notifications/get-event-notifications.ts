//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export const eventNotificationApi = async (org_id: any) => {
  const response = await fetch(`/api/events/notifications/all/${org_id}`);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating events');
  }

  return await response.json();
};

type QueryFnType = typeof eventNotificationApi;
type options = QueryConfigType<QueryFnType>;
export const useGetAllEventNotifactionQuery = (
  org_id: any,
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['eventNotifications'],
    queryFn: () => eventNotificationApi(org_id),
    ...config,
  });
};
