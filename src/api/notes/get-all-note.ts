import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export type TFilter = {
  items_per_page: number;
  current_page: number;
  title: string;
  notes: string;
  status: string;
};

async function noteView(client_id?: number, filter?: TFilter | undefined) {
  const baseUrl = `/api/notes`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    client_id: client_id,
    ...(filter ? filter : {}),
  });
  const response = await fetch(apiUrl, { method: 'GET' });
  const json = await response.json();
  return json;
}

type QueryFnType = typeof noteView;
export const useNoteViewQuery = (
  client_id?: number,
  filter?: TFilter | undefined,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.notes?.getAll, client_id, filter],
    queryFn: () => noteView(client_id, filter),
    ...config,
  });
};
