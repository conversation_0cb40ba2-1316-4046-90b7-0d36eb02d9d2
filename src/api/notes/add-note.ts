import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const addNote = async (body: any) => {
  const response = await fetch(`/api/notes`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating note');
  }
  return response.json();
};

type QueryFnType = typeof addNote;

export const useAddNoteApi = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: ToastMessages.somethingWrong || err?.message,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'New note created',
      });
      queryClient.invalidateQueries([queryKey.notes?.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notes?.create],
    mutationFn: addNote,
    ...config,
  });
};
