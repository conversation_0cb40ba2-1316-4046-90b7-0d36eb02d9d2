//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getNotesByBookingId(id: number) {
  const response = await fetch(`/api/notes/${id}/get-notes-by-id`);
  if (!response.ok) throw new Error('Error fetching notes');
  return response.json();
}

type QueryFnType = typeof getNotesByBookingId;

export const useGetNotesByBookingId = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.notes.getNoteByBookingId, id],
    queryFn: () => getNotesByBookingId(id),
    ...config,
  });
};
