import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const updateNote = async (body: any) => {
  const response = await fetch(`/api/notes`, {
    method: 'PATCH',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating Note data');
  }
  return response.json();
};

type QueryFnType = typeof updateNote;

export const useUpdateNoteAPI = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();

  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Note data updated',
      });
      queryClient.invalidateQueries([queryKey.notes.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notes.update],
    mutationFn: updateNote,
    ...config,
  });
};
