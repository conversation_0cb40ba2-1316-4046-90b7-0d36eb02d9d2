import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const deleteNote = async (body: any) => {
  const response = await fetch(`/api/notes`, {
    method: 'DELETE',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error deleting note');
  }
  return response.json();
};

type QueryFnType = typeof deleteNote;

export const useDeleteNoteApi = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'note deleted successfully',
      });
      queryClient.invalidateQueries([queryKey.notes.getAll]);
    },
    retry: false,
    mutationKey: [queryKey.notes.delete],
    mutationFn: deleteNote,
    ...config,
  });
};
