import { query<PERSON><PERSON> } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';

async function getTransactions(id: any) {
  const baseUrl = `/api/transactions/client/${id}`;
  const response = await fetch(baseUrl, { method: 'GET' });
  const data = await response.json();
  if (!response.ok) throw new Error(data?.message);

  return data;
}
type QueryFnType = typeof getTransactions;

export const useGetClientTransactionsQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.transactions.getByClientId, id],
    queryFn: () => getTransactions(id),
    ...config,
  });
};
