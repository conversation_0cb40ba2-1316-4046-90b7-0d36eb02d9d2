import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getAllAuditLogs(filter = {}) {
  const baseUrl = '/api/audit-logs';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch audit logs');
  }
  const data = await response.json();
  return data;
}
