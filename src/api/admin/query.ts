import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { getAllAuditLogs } from './service';
import { queryKey } from '@/constants/query-key';

export const useGetAllAuditLogsQuery = (
  filter?: any,
  config?: QueryConfigType<typeof getAllAuditLogs>
) => {
  return useQuery<ExtractFnReturnType<typeof getAllAuditLogs>>({
    queryKey: [queryKey.auditLogs.getAll, filter],
    queryFn: () => getAllAuditLogs(filter),
    ...config,
  });
};
